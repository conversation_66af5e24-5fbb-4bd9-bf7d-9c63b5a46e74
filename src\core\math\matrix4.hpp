// src/core/math/matrix4.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// 4x4 Matrix mathematics for transformations

#pragma once

#include "vec3.hpp"
#include <array>
#include <iostream>

namespace photon {

/**
 * @brief 4x4 Matrix class for transformations
 * 
 * Column-major storage for OpenGL compatibility.
 * Matrix multiplication follows standard mathematical convention.
 */
class Matrix4 {
public:
    // Column-major storage: m[column][row]
    std::array<std::array<float, 4>, 4> m;
    
    // Constructors
    Matrix4() { setIdentity(); }
    
    Matrix4(float m00, float m01, float m02, float m03,
           float m10, float m11, float m12, float m13,
           float m20, float m21, float m22, float m23,
           float m30, float m31, float m32, float m33) {
        // Row-major input, column-major storage
        m[0][0] = m00; m[1][0] = m01; m[2][0] = m02; m[3][0] = m03;
        m[0][1] = m10; m[1][1] = m11; m[2][1] = m12; m[3][1] = m13;
        m[0][2] = m20; m[1][2] = m21; m[2][2] = m22; m[3][2] = m23;
        m[0][3] = m30; m[1][3] = m31; m[2][3] = m32; m[3][3] = m33;
    }
    
    // Copy constructor and assignment
    Matrix4(const Matrix4& other) = default;
    Matrix4& operator=(const Matrix4& other) = default;
    
    // Element access
    float& operator()(int row, int col) { return m[col][row]; }
    const float& operator()(int row, int col) const { return m[col][row]; }
    
    std::array<float, 4>& operator[](int col) { return m[col]; }
    const std::array<float, 4>& operator[](int col) const { return m[col]; }
    
    // Matrix operations
    Matrix4 operator+(const Matrix4& other) const;
    Matrix4 operator-(const Matrix4& other) const;
    Matrix4 operator*(const Matrix4& other) const;
    Matrix4 operator*(float scalar) const;
    Matrix4 operator/(float scalar) const;
    
    Matrix4& operator+=(const Matrix4& other);
    Matrix4& operator-=(const Matrix4& other);
    Matrix4& operator*=(const Matrix4& other);
    Matrix4& operator*=(float scalar);
    Matrix4& operator/=(float scalar);
    
    // Comparison
    bool operator==(const Matrix4& other) const;
    bool operator!=(const Matrix4& other) const;
    
    // Vector transformation
    Vec3 transformPoint(const Vec3& point) const;
    Vec3 transformVector(const Vec3& vector) const;
    Vec3 transformNormal(const Vec3& normal) const;
    
    // Matrix properties
    Matrix4 transpose() const;
    Matrix4 inverse() const;
    float determinant() const;
    bool isInvertible() const;
    
    // Matrix initialization
    void setIdentity();
    void setZero();
    
    // Static factory methods
    static Matrix4 identity();
    static Matrix4 zero();
    static Matrix4 translation(const Vec3& t);
    static Matrix4 translation(float x, float y, float z);
    static Matrix4 scale(const Vec3& s);
    static Matrix4 scale(float x, float y, float z);
    static Matrix4 scale(float uniform);
    static Matrix4 rotationX(float angle);
    static Matrix4 rotationY(float angle);
    static Matrix4 rotationZ(float angle);
    static Matrix4 rotation(const Vec3& axis, float angle);
    static Matrix4 lookAt(const Vec3& eye, const Vec3& target, const Vec3& up);
    static Matrix4 perspective(float fovy, float aspect, float near, float far);
    static Matrix4 orthographic(float left, float right, float bottom, float top, float near, float far);
    
    // Decomposition
    bool decompose(Vec3& translation, Vec3& rotation, Vec3& scale) const;
    
    // Utility
    bool hasNaN() const;
    bool hasInf() const;
    
    // Get raw data pointer (for OpenGL)
    const float* data() const { return &m[0][0]; }
    float* data() { return &m[0][0]; }

private:
    // Helper for matrix inversion
    Matrix4 adjugate() const;
    float minor(int row, int col) const;
    float cofactor(int row, int col) const;
};

// Non-member operators
Matrix4 operator*(float scalar, const Matrix4& matrix);

// Stream operator
std::ostream& operator<<(std::ostream& os, const Matrix4& matrix);

/**
 * @brief Transform class combining translation, rotation, and scale
 */
class Transform {
public:
    Transform() = default;
    Transform(const Matrix4& matrix) : m_matrix(matrix), m_inverse(matrix.inverse()) {}
    Transform(const Matrix4& matrix, const Matrix4& inverse) : m_matrix(matrix), m_inverse(inverse) {}
    
    // Transform operations
    Point3 transformPoint(const Point3& p) const { return m_matrix.transformPoint(p); }
    Vec3 transformVector(const Vec3& v) const { return m_matrix.transformVector(v); }
    Normal3 transformNormal(const Normal3& n) const { return m_inverse.transpose().transformVector(n).normalized(); }
    Ray transformRay(const Ray& ray) const;
    
    // Inverse transform operations
    Point3 inverseTransformPoint(const Point3& p) const { return m_inverse.transformPoint(p); }
    Vec3 inverseTransformVector(const Vec3& v) const { return m_inverse.transformVector(v); }
    Normal3 inverseTransformNormal(const Normal3& n) const { return m_matrix.transpose().transformVector(n).normalized(); }
    Ray inverseTransformRay(const Ray& ray) const;
    
    // Transform composition
    Transform operator*(const Transform& other) const;
    Transform& operator*=(const Transform& other);
    
    // Get matrices
    const Matrix4& getMatrix() const { return m_matrix; }
    const Matrix4& getInverse() const { return m_inverse; }
    
    // Transform properties
    Transform inverse() const { return Transform(m_inverse, m_matrix); }
    bool isIdentity() const;
    
    // Static factory methods
    static Transform identity();
    static Transform translation(const Vec3& t);
    static Transform scale(const Vec3& s);
    static Transform scale(float uniform);
    static Transform rotationX(float angle);
    static Transform rotationY(float angle);
    static Transform rotationZ(float angle);
    static Transform rotation(const Vec3& axis, float angle);
    static Transform lookAt(const Vec3& eye, const Vec3& target, const Vec3& up);

private:
    Matrix4 m_matrix = Matrix4::identity();
    Matrix4 m_inverse = Matrix4::identity();
};

} // namespace photon
