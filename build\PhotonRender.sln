﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{42D57C87-C28E-3FF6-BE15-45B72866C2FC}"
	ProjectSection(ProjectDependencies) = postProject
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313} = {6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8} = {21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297} = {B1742191-EC90-3C18-A80B-3FEBFF3C2297}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{0110854C-3288-3537-8468-2BC6C3A2BE27}"
	ProjectSection(ProjectDependencies) = postProject
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC} = {42D57C87-C28E-3FF6-BE15-45B72866C2FC}
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313} = {6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "photon_core_simple", "photon_core_simple.vcxproj", "{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}"
	ProjectSection(ProjectDependencies) = postProject
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313} = {6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "photon_test_simple", "photon_test_simple.vcxproj", "{B1742191-EC90-3C18-A80B-3FEBFF3C2297}"
	ProjectSection(ProjectDependencies) = postProject
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313} = {6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8} = {21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.Debug|x64.ActiveCfg = Debug|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.Debug|x64.Build.0 = Debug|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.Release|x64.ActiveCfg = Release|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.Release|x64.Build.0 = Release|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{42D57C87-C28E-3FF6-BE15-45B72866C2FC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{0110854C-3288-3537-8468-2BC6C3A2BE27}.Debug|x64.ActiveCfg = Debug|x64
		{0110854C-3288-3537-8468-2BC6C3A2BE27}.Release|x64.ActiveCfg = Release|x64
		{0110854C-3288-3537-8468-2BC6C3A2BE27}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{0110854C-3288-3537-8468-2BC6C3A2BE27}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.Debug|x64.ActiveCfg = Debug|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.Debug|x64.Build.0 = Debug|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.Release|x64.ActiveCfg = Release|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.Release|x64.Build.0 = Release|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6AF6EFBE-EB68-34F8-BE6D-94BDA19AB313}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.Debug|x64.ActiveCfg = Debug|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.Debug|x64.Build.0 = Debug|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.Release|x64.ActiveCfg = Release|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.Release|x64.Build.0 = Release|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{21D81E2F-37BE-35BC-8279-9A6CFF5F78A8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.Debug|x64.ActiveCfg = Debug|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.Debug|x64.Build.0 = Debug|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.Release|x64.ActiveCfg = Release|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.Release|x64.Build.0 = Release|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B1742191-EC90-3C18-A80B-3FEBFF3C2297}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A1FC79B9-54B8-350C-866D-8508730032CB}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
