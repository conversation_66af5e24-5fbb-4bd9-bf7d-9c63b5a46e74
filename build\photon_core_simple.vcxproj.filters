﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\math\matrix4.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\sampler\sampler.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\renderer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\scene\scene.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\scene\camera.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\scene\light.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\integrator\integrator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\xampp\htdocs\progetti\photon-render\src\core\material\material.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\xampp\htdocs\progetti\photon-render\build\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{F1A0A878-5B6D-37B4-8A50-DC7AEB0787BB}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
