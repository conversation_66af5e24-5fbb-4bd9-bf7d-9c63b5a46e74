// include/photon/photon.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Main public API header

#pragma once

// Version information
#include "version.hpp"

// Core mathematics
#include "../../src/core/math/vec3.hpp"
#include "../../src/core/math/ray.hpp"

// Core rendering
#include "../../src/core/renderer.hpp"

/**
 * @brief PhotonRender namespace
 * 
 * Contains all public API classes and functions for the PhotonRender engine.
 * This is the main entry point for applications using PhotonRender.
 */
namespace photon {

/**
 * @brief Initialize PhotonRender engine
 * 
 * Must be called before using any PhotonRender functionality.
 * Initializes internal systems and checks hardware capabilities.
 * 
 * @return true if initialization successful, false otherwise
 */
bool initialize();

/**
 * @brief Shutdown PhotonRender engine
 * 
 * Cleans up internal resources and shuts down the engine.
 * Should be called when done using PhotonRender.
 */
void shutdown();

/**
 * @brief Get engine version information
 * 
 * @return Version structure with major, minor, patch versions
 */
Version getVersion();

/**
 * @brief Check if GPU acceleration is available
 * 
 * @return true if CUDA/OptiX is available, false otherwise
 */
bool hasGPUAcceleration();

/**
 * @brief Get available GPU devices
 * 
 * @return Vector of GPU device information
 */
std::vector<GPUDevice> getGPUDevices();

/**
 * @brief Engine capabilities structure
 */
struct Capabilities {
    bool hasEmbree;        ///< Intel Embree available
    bool hasCUDA;          ///< NVIDIA CUDA available  
    bool hasOptiX;         ///< NVIDIA OptiX available
    bool hasOpenMP;        ///< OpenMP threading available
    bool hasTBB;           ///< Intel TBB available
    int maxThreads;        ///< Maximum CPU threads
    int gpuCount;          ///< Number of GPU devices
};

/**
 * @brief Get engine capabilities
 * 
 * @return Capabilities structure with available features
 */
Capabilities getCapabilities();

/**
 * @brief GPU device information
 */
struct GPUDevice {
    int deviceId;          ///< Device ID
    std::string name;      ///< Device name
    size_t totalMemory;    ///< Total memory in bytes
    size_t freeMemory;     ///< Free memory in bytes
    int computeCapability; ///< CUDA compute capability
    bool hasRTCores;       ///< Has RT cores (RTX)
    bool hasTensorCores;   ///< Has Tensor cores
};

/**
 * @brief Logging levels
 */
enum class LogLevel {
    Debug,    ///< Debug information
    Info,     ///< General information
    Warning,  ///< Warning messages
    Error,    ///< Error messages
    Fatal     ///< Fatal errors
};

/**
 * @brief Set logging level
 * 
 * @param level Minimum logging level to output
 */
void setLogLevel(LogLevel level);

/**
 * @brief Set custom log callback
 * 
 * @param callback Function to handle log messages
 */
void setLogCallback(std::function<void(LogLevel, const std::string&)> callback);

/**
 * @brief Performance profiler
 */
class Profiler {
public:
    static void begin(const std::string& name);
    static void end(const std::string& name);
    static void reset();
    static std::string getReport();
};

/**
 * @brief RAII profiler scope
 */
class ProfileScope {
public:
    ProfileScope(const std::string& name) : m_name(name) {
        Profiler::begin(m_name);
    }
    
    ~ProfileScope() {
        Profiler::end(m_name);
    }
    
private:
    std::string m_name;
};

// Profiling macro
#define PHOTON_PROFILE(name) photon::ProfileScope _prof(name)

} // namespace photon

// Convenience macros
#define PHOTON_VERSION_MAJOR 0
#define PHOTON_VERSION_MINOR 1  
#define PHOTON_VERSION_PATCH 0
#define PHOTON_VERSION_STRING "0.1.0"
