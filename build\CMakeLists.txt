# CMakeLists_simple.txt
# PhotonRender - Simplified version for initial testing
# Minimal CMake configuration without external dependencies

cmake_minimum_required(VERSION 3.16)
project(PhotonRender VERSION 0.1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Include directories
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

# Compiler flags
if(MSVC)
    add_compile_options(/W4 /WX- /MP /permissive-)
    add_compile_options(/fp:fast)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(/Od /Zi)
    else()
        add_compile_options(/O2 /DNDEBUG)
    endif()
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# Core library (simplified - no external dependencies)
add_library(photon_core_simple STATIC
    # Math library
    src/core/math/matrix4.cpp
    src/core/sampler/sampler.cpp
    
    # Core rendering (simplified)
    src/core/renderer.cpp
    src/core/scene/scene.cpp
    src/core/scene/camera.cpp
    src/core/scene/light.cpp
    src/core/integrator/integrator.cpp
    src/core/material/material.cpp
)

# Simple test executable
add_executable(photon_test_simple
    src/test_simple.cpp
)

target_link_libraries(photon_test_simple photon_core_simple)

# Installation
install(TARGETS photon_core_simple photon_test_simple
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Status messages
message(STATUS "")
message(STATUS "PhotonRender ${PROJECT_VERSION} - Simplified Build")
message(STATUS "")
message(STATUS "Build type:        ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard:      ${CMAKE_CXX_STANDARD}")
message(STATUS "Compiler:          ${CMAKE_CXX_COMPILER_ID}")
message(STATUS "")
