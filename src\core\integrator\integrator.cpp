// src/core/integrator/integrator.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Integrator implementations

#include "integrator.hpp"
#include "../sampler/sampler.hpp"
#include "../material/material.hpp"
#include "../scene/light.hpp"
#include <algorithm>

namespace photon {

// DirectLightingIntegrator implementation
DirectLightingIntegrator::DirectLightingIntegrator(int maxDepth, int lightSamples)
    : m_maxDepth(maxDepth), m_lightSamples(lightSamples) {
}

Color3 DirectLightingIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    Intersection isect;
    if (!scene.intersect(ray, isect)) {
        return Color3(0); // Background
    }
    
    // Get material emission
    Color3 L = isect.material ? isect.material->Le(isect, -ray.d) : Color3(0);
    
    // Add direct lighting
    L += sampleDirectLighting(isect, scene, sampler);
    
    return L;
}

Color3 DirectLightingIntegrator::sampleDirectLighting(const Intersection& isect, const Scene& scene, Sampler& sampler) const {
    Color3 L(0);
    
    // Sample all lights
    for (const auto& light : scene.getLights()) {
        L += sampleOneLight(isect, scene, *light, sampler);
    }
    
    return L;
}

Color3 DirectLightingIntegrator::sampleOneLight(const Intersection& isect, const Scene& scene,
                                               const Light& light, Sampler& sampler) const {
    // Sample light
    LightSample lightSample = light.sample(isect, sampler);
    if (!lightSample.isValid()) return Color3(0);

    // Check visibility
    Ray shadowRay(isect.p, lightSample.wi, 0.001f, lightSample.distance - 0.001f);
    if (scene.intersectShadow(shadowRay)) {
        return Color3(0); // Occluded
    }

    // Evaluate BSDF (need to pass wo direction)
    if (!isect.material) return Color3(0);
    Vec3 wo = -lightSample.wi; // Outgoing direction (opposite of light direction)
    Color3 f = isect.material->f(isect, wo, lightSample.wi);

    // Compute contribution
    float cosTheta = std::max(0.0f, lightSample.wi.dot(isect.n));
    return f * lightSample.Li * cosTheta / lightSample.pdf;
}

// PathTracingIntegrator implementation
PathTracingIntegrator::PathTracingIntegrator(int maxDepth, int rrDepth, int lightSamples)
    : m_maxDepth(maxDepth), m_rrDepth(rrDepth), m_lightSamples(lightSamples) {
}

Color3 PathTracingIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    Color3 L(0);
    Color3 beta(1); // Path throughput
    Ray currentRay = ray;
    
    for (int depth = 0; depth < m_maxDepth; ++depth) {
        Intersection isect;
        if (!scene.intersect(currentRay, isect)) {
            break; // Hit background
        }
        
        // Add emission
        if (isect.material) {
            L += beta * isect.material->Le(isect, -currentRay.d);
        }
        
        // Add direct lighting
        L += beta * sampleDirectLighting(isect, scene, sampler, -currentRay.d);
        
        // Sample BSDF for next direction
        if (!isect.material) break;
        BSDFSample bsdfSample = sampleBSDF(isect, -currentRay.d, sampler);
        if (!bsdfSample.isValid()) break;
        
        // Update path throughput
        float cosTheta = std::max(0.0f, bsdfSample.wi.dot(isect.n));
        beta *= bsdfSample.f * cosTheta / bsdfSample.pdf;
        
        // Russian roulette
        if (depth >= m_rrDepth) {
            float q = std::max(0.05f, 1.0f - beta.maxComponent());
            if (sampler.get1D() < q) break;
            beta /= (1.0f - q);
        }
        
        // Generate next ray
        currentRay = Ray(isect.p, bsdfSample.wi);
    }
    
    return L;
}

Color3 PathTracingIntegrator::sampleDirectLighting(const Intersection& isect, const Scene& scene, 
                                                  Sampler& sampler, const Vec3& wo) const {
    // Simplified direct lighting (same as DirectLightingIntegrator for now)
    Color3 L(0);
    for (const auto& light : scene.getLights()) {
        LightSample lightSample = light->sample(isect, sampler);
        if (!lightSample.isValid()) continue;
        
        Ray shadowRay(isect.p, lightSample.wi, 0.001f, lightSample.distance - 0.001f);
        if (scene.intersectShadow(shadowRay)) continue;
        
        if (!isect.material) continue;
        Color3 f = isect.material->f(isect, wo, lightSample.wi);
        float cosTheta = std::max(0.0f, lightSample.wi.dot(isect.n));
        L += f * lightSample.Li * cosTheta / lightSample.pdf;
    }
    return L;
}

PathTracingIntegrator::BSDFSample PathTracingIntegrator::sampleBSDF(const Intersection& isect, const Vec3& wo, Sampler& sampler) const {
    if (!isect.material) return BSDFSample();
    return isect.material->sample(isect, wo, sampler);
}

Color3 PathTracingIntegrator::evaluateBSDF(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    if (!isect.material) return Color3(0);
    return isect.material->f(isect, wo, wi);
}

float PathTracingIntegrator::getBSDFPdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const {
    if (!isect.material) return 0.0f;
    return isect.material->pdf(isect, wo, wi);
}

float PathTracingIntegrator::powerHeuristic(float nf, float fPdf, float ng, float gPdf) const {
    float f = nf * fPdf;
    float g = ng * gPdf;
    return (f * f) / (f * f + g * g);
}

// AmbientOcclusionIntegrator implementation
AmbientOcclusionIntegrator::AmbientOcclusionIntegrator(float maxDistance, int samples)
    : m_maxDistance(maxDistance), m_samples(samples) {
}

Color3 AmbientOcclusionIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    Intersection isect;
    if (!scene.intersect(ray, isect)) {
        return Color3(1); // Background is white
    }
    
    float ao = computeAO(isect, scene, sampler);
    return Color3(ao);
}

float AmbientOcclusionIntegrator::computeAO(const Intersection& isect, const Scene& scene, Sampler& sampler) const {
    float occlusion = 0.0f;
    
    for (int i = 0; i < m_samples; ++i) {
        Vec3 wi = sampleHemisphere(isect.n, sampler);
        Ray aoRay(isect.p, wi, 0.001f, m_maxDistance);
        
        if (scene.intersectShadow(aoRay)) {
            occlusion += 1.0f;
        }
    }
    
    return 1.0f - (occlusion / m_samples);
}

Vec3 AmbientOcclusionIntegrator::sampleHemisphere(const Normal3& normal, Sampler& sampler) const {
    float u1 = sampler.get1D();
    float u2 = sampler.get1D();
    
    // Cosine-weighted hemisphere sampling
    float cosTheta = std::sqrt(u1);
    float sinTheta = std::sqrt(1.0f - u1);
    float phi = 2.0f * M_PI * u2;
    
    Vec3 sample(sinTheta * std::cos(phi), sinTheta * std::sin(phi), cosTheta);
    
    // Transform to world space
    Vec3 nt = (std::abs(normal.x) > 0.1f) ? Vec3(0, 1, 0) : Vec3(1, 0, 0);
    Vec3 tangent = normal.cross(nt).normalized();
    Vec3 bitangent = normal.cross(tangent);
    
    return sample.x * tangent + sample.y * bitangent + sample.z * normal;
}

// NormalIntegrator implementation
Color3 NormalIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    Intersection isect;
    if (!scene.intersect(ray, isect)) {
        return Color3(0); // Background
    }
    
    // Convert normal from [-1,1] to [0,1] range
    Normal3 n = isect.n;
    return Color3((n.x + 1.0f) * 0.5f, (n.y + 1.0f) * 0.5f, (n.z + 1.0f) * 0.5f);
}

// DepthIntegrator implementation
DepthIntegrator::DepthIntegrator(float maxDepth) : m_maxDepth(maxDepth) {
}

Color3 DepthIntegrator::Li(const Ray& ray, const Scene& scene, Sampler& sampler) const {
    Intersection isect;
    if (!scene.intersect(ray, isect)) {
        return Color3(1); // Background is white (far)
    }
    
    // Normalize depth to [0,1] range
    float depth = std::min(isect.t / m_maxDepth, 1.0f);
    float gray = 1.0f - depth; // Closer objects are brighter
    
    return Color3(gray);
}

} // namespace photon
