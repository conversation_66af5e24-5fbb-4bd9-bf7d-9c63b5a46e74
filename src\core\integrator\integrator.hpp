// src/core/integrator/integrator.hpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Rendering integrator system

#pragma once

#include "../math/vec3.hpp"
#include "../math/ray.hpp"
#include "../scene/scene.hpp"
#include <memory>

namespace photon {

// Forward declarations
class Scene;
class Sampler;
class Material;
class Light;

/**
 * @brief Abstract base integrator class
 * 
 * Integrators implement different rendering algorithms (path tracing, direct lighting, etc.)
 */
class Integrator {
public:
    Integrator() = default;
    virtual ~Integrator() = default;
    
    // Non-copyable
    Integrator(const Integrator&) = delete;
    Integrator& operator=(const Integrator&) = delete;
    
    /**
     * @brief Compute radiance along a ray
     * 
     * @param ray The ray to trace
     * @param scene The scene to render
     * @param sampler Random number sampler
     * @return Radiance value (RGB color)
     */
    virtual Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const = 0;
    
    /**
     * @brief Preprocess the scene (optional)
     * 
     * Called once before rendering begins. Can be used for light sampling preparation, etc.
     */
    virtual void preprocess(const Scene& scene) {}
    
    /**
     * @brief Get integrator name for debugging
     */
    virtual std::string getName() const = 0;
};

/**
 * @brief Direct lighting integrator
 * 
 * Only computes direct illumination (no global illumination)
 */
class DirectLightingIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param maxDepth Maximum ray depth
     * @param lightSamples Number of light samples per intersection
     */
    DirectLightingIntegrator(int maxDepth = 5, int lightSamples = 1);
    
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "DirectLighting"; }

private:
    int m_maxDepth;
    int m_lightSamples;
    
    /**
     * @brief Sample direct lighting at intersection point
     */
    Color3 sampleDirectLighting(const Intersection& isect, const Scene& scene, Sampler& sampler) const;
    
    /**
     * @brief Sample one light source
     */
    Color3 sampleOneLight(const Intersection& isect, const Scene& scene, 
                         const Light& light, Sampler& sampler) const;
};

/**
 * @brief Path tracing integrator
 * 
 * Implements unbiased Monte Carlo path tracing with multiple importance sampling
 */
class PathTracingIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param maxDepth Maximum path depth
     * @param rrDepth Depth to start Russian roulette
     * @param lightSamples Number of light samples per intersection
     */
    PathTracingIntegrator(int maxDepth = 8, int rrDepth = 3, int lightSamples = 1);
    
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "PathTracing"; }

private:
    int m_maxDepth;
    int m_rrDepth;
    int m_lightSamples;
    
    /**
     * @brief Sample direct lighting with multiple importance sampling
     */
    Color3 sampleDirectLighting(const Intersection& isect, const Scene& scene, 
                               Sampler& sampler, const Vec3& wo) const;
    
    /**
     * @brief Sample BSDF for next path direction
     */
    struct BSDFSample {
        Color3 f;           // BSDF value
        Vec3 wi;            // Incident direction
        float pdf;          // Probability density
        bool isDelta;       // Is delta distribution (perfect reflection/transmission)
    };
    
    BSDFSample sampleBSDF(const Intersection& isect, const Vec3& wo, Sampler& sampler) const;
    
    /**
     * @brief Evaluate BSDF
     */
    Color3 evaluateBSDF(const Intersection& isect, const Vec3& wo, const Vec3& wi) const;
    
    /**
     * @brief Get BSDF PDF
     */
    float getBSDFPdf(const Intersection& isect, const Vec3& wo, const Vec3& wi) const;
    
    /**
     * @brief Power heuristic for multiple importance sampling
     */
    float powerHeuristic(float nf, float fPdf, float ng, float gPdf) const;
};

/**
 * @brief Ambient occlusion integrator
 * 
 * Simple ambient occlusion for fast preview rendering
 */
class AmbientOcclusionIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param maxDistance Maximum occlusion distance
     * @param samples Number of AO samples
     */
    AmbientOcclusionIntegrator(float maxDistance = 1.0f, int samples = 16);
    
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "AmbientOcclusion"; }

private:
    float m_maxDistance;
    int m_samples;
    
    /**
     * @brief Sample hemisphere for ambient occlusion
     */
    Vec3 sampleHemisphere(const Normal3& normal, Sampler& sampler) const;
    
    /**
     * @brief Compute ambient occlusion factor
     */
    float computeAO(const Intersection& isect, const Scene& scene, Sampler& sampler) const;
};

/**
 * @brief Normal visualization integrator
 * 
 * Visualizes surface normals as colors (useful for debugging)
 */
class NormalIntegrator : public Integrator {
public:
    NormalIntegrator() = default;
    
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "Normal"; }
};

/**
 * @brief Depth visualization integrator
 * 
 * Visualizes depth as grayscale (useful for debugging)
 */
class DepthIntegrator : public Integrator {
public:
    /**
     * @brief Constructor
     * 
     * @param maxDepth Maximum depth for normalization
     */
    DepthIntegrator(float maxDepth = 100.0f);
    
    Color3 Li(const Ray& ray, const Scene& scene, Sampler& sampler) const override;
    std::string getName() const override { return "Depth"; }

private:
    float m_maxDepth;
};

} // namespace photon
