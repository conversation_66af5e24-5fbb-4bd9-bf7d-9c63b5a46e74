
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
      Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
      
      Compilazione avviata 17/06/2025 16:44:45.
      Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" sul nodo 1 (destinazioni predefinite).
      PrepareForBuild:
        Creazione directory "Debug\\".
        Creazione directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creazione di "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Eliminazione del file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" in corso.
        Aggiornamento timestamp di "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinazioni predefinite) completata.
      
      Compilazione completata.
          Avvisi: 0
          Errori: 0
      
      Tempo trascorso 00:00:00.84
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/4.0.3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
      Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
      
      Compilazione avviata 17/06/2025 16:44:46.
      Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.vcxproj" sul nodo 1 (destinazioni predefinite).
      PrepareForBuild:
        Creazione directory "Debug\\".
        Creazione directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creazione di "Debug\\CompilerIdC.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Eliminazione del file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" in corso.
        Aggiornamento timestamp di "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdC\\CompilerIdC.vcxproj" (destinazioni predefinite) completata.
      
      Compilazione completata.
          Avvisi: 0
          Errori: 0
      
      Tempo trascorso 00:00:00.73
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/4.0.3/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-31ap2v"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-31ap2v"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-31ap2v'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_505d1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:47.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-31ap2v\\cmTC_505d1.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_505d1.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-31ap2v\\Debug\\".
          Creazione directory "cmTC_505d1.dir\\Debug\\cmTC_505d1.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_505d1.dir\\Debug\\cmTC_505d1.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_505d1.dir\\Debug\\\\" /Fd"cmTC_505d1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          CMakeCXXCompilerABI.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_505d1.dir\\Debug\\\\" /Fd"cmTC_505d1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-31ap2v\\Debug\\cmTC_505d1.exe" /INCREMENTAL /ILK:"cmTC_505d1.dir\\Debug\\cmTC_505d1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-31ap2v/Debug/cmTC_505d1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-31ap2v/Debug/cmTC_505d1.lib" /MACHINE:X64  /machine:x64 cmTC_505d1.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_505d1.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-31ap2v\\Debug\\cmTC_505d1.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_505d1.dir\\Debug\\cmTC_505d1.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_505d1.dir\\Debug\\cmTC_505d1.tlog\\cmTC_505d1.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-31ap2v\\cmTC_505d1.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.72
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-n9kum9"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-n9kum9"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-n9kum9'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_f0241.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:48.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n9kum9\\cmTC_f0241.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_f0241.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n9kum9\\Debug\\".
          Creazione directory "cmTC_f0241.dir\\Debug\\cmTC_f0241.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_f0241.dir\\Debug\\cmTC_f0241.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f0241.dir\\Debug\\\\" /Fd"cmTC_f0241.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          CMakeCCompilerABI.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f0241.dir\\Debug\\\\" /Fd"cmTC_f0241.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n9kum9\\Debug\\cmTC_f0241.exe" /INCREMENTAL /ILK:"cmTC_f0241.dir\\Debug\\cmTC_f0241.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-n9kum9/Debug/cmTC_f0241.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-n9kum9/Debug/cmTC_f0241.lib" /MACHINE:X64  /machine:x64 cmTC_f0241.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_f0241.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n9kum9\\Debug\\cmTC_f0241.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_f0241.dir\\Debug\\cmTC_f0241.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_f0241.dir\\Debug\\cmTC_f0241.tlog\\cmTC_f0241.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-n9kum9\\cmTC_f0241.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.70
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:31 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pk684u"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pk684u"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pk684u'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_01240.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:49.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\cmTC_01240.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_01240.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\Debug\\".
          Creazione directory "cmTC_01240.dir\\Debug\\cmTC_01240.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_01240.dir\\Debug\\cmTC_01240.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_01240.dir\\Debug\\\\" /Fd"cmTC_01240.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_01240.dir\\Debug\\\\" /Fd"cmTC_01240.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\src.c"
        C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\src.c(1,10): fatal error C1083: Non è possibile aprire il file inclusione: 'pthread.h': No such file or directory [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\cmTC_01240.vcxproj]
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\cmTC_01240.vcxproj" (destinazioni predefinite) NON COMPLETATA.
        
        Compilazione NON RIUSCITA.
        
        "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\cmTC_01240.vcxproj" (destinazione predefinita) (1) ->
        (destinazione: ClCompile) -> 
          C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\src.c(1,10): fatal error C1083: Non è possibile aprire il file inclusione: 'pthread.h': No such file or directory [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pk684u\\cmTC_01240.vcxproj]
        
            Avvisi: 0
            Errori: 1
        
        Tempo trascorso 00:00:00.57
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:31 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-0im0qn"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-0im0qn"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-0im0qn'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_3a6dc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:50.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\cmTC_3a6dc.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_3a6dc.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\Debug\\".
          Creazione directory "cmTC_3a6dc.dir\\Debug\\cmTC_3a6dc.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_3a6dc.dir\\Debug\\cmTC_3a6dc.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3a6dc.dir\\Debug\\\\" /Fd"cmTC_3a6dc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          CheckFunctionExists.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3a6dc.dir\\Debug\\\\" /Fd"cmTC_3a6dc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\Debug\\cmTC_3a6dc.exe" /INCREMENTAL /ILK:"cmTC_3a6dc.dir\\Debug\\cmTC_3a6dc.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-0im0qn/Debug/cmTC_3a6dc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-0im0qn/Debug/cmTC_3a6dc.lib" /MACHINE:X64  /machine:x64 cmTC_3a6dc.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: impossibile aprire il file 'pthreads.lib' [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\cmTC_3a6dc.vcxproj]
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\cmTC_3a6dc.vcxproj" (destinazioni predefinite) NON COMPLETATA.
        
        Compilazione NON RIUSCITA.
        
        "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\cmTC_3a6dc.vcxproj" (destinazione predefinita) (1) ->
        (destinazione: Link) -> 
          LINK : fatal error LNK1104: impossibile aprire il file 'pthreads.lib' [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0im0qn\\cmTC_3a6dc.vcxproj]
        
            Avvisi: 0
            Errori: 1
        
        Tempo trascorso 00:00:00.64
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:31 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-p9ftw8"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-p9ftw8"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-p9ftw8'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_7dcb3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:51.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\cmTC_7dcb3.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_7dcb3.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\Debug\\".
          Creazione directory "cmTC_7dcb3.dir\\Debug\\cmTC_7dcb3.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_7dcb3.dir\\Debug\\cmTC_7dcb3.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7dcb3.dir\\Debug\\\\" /Fd"cmTC_7dcb3.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          CheckFunctionExists.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7dcb3.dir\\Debug\\\\" /Fd"cmTC_7dcb3.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\CheckFunctionExists.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\Debug\\cmTC_7dcb3.exe" /INCREMENTAL /ILK:"cmTC_7dcb3.dir\\Debug\\cmTC_7dcb3.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-p9ftw8/Debug/cmTC_7dcb3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-p9ftw8/Debug/cmTC_7dcb3.lib" /MACHINE:X64  /machine:x64 cmTC_7dcb3.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: impossibile aprire il file 'pthread.lib' [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\cmTC_7dcb3.vcxproj]
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\cmTC_7dcb3.vcxproj" (destinazioni predefinite) NON COMPLETATA.
        
        Compilazione NON RIUSCITA.
        
        "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\cmTC_7dcb3.vcxproj" (destinazione predefinita) (1) ->
        (destinazione: Link) -> 
          LINK : fatal error LNK1104: impossibile aprire il file 'pthread.lib' [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p9ftw8\\cmTC_7dcb3.vcxproj]
        
            Avvisi: 0
            Errori: 1
        
        Tempo trascorso 00:00:00.63
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:32 (find_package)"
    description: "Detecting C OpenMP compiler info"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2g0ha8"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2g0ha8"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_C_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2g0ha8'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_63989.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:52.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\cmTC_63989.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_63989.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\Debug\\".
          Creazione directory "cmTC_63989.dir\\Debug\\cmTC_63989.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_63989.dir\\Debug\\cmTC_63989.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /Fo"cmTC_63989.dir\\Debug\\\\" /Fd"cmTC_63989.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\OpenMPTryFlag.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          OpenMPTryFlag.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /Fo"cmTC_63989.dir\\Debug\\\\" /Fd"cmTC_63989.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\OpenMPTryFlag.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\Debug\\cmTC_63989.exe" /INCREMENTAL /ILK:"cmTC_63989.dir\\Debug\\cmTC_63989.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2g0ha8/Debug/cmTC_63989.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2g0ha8/Debug/cmTC_63989.lib" /MACHINE:X64  /machine:x64 cmTC_63989.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_63989.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\Debug\\cmTC_63989.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_63989.dir\\Debug\\cmTC_63989.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_63989.dir\\Debug\\cmTC_63989.tlog\\cmTC_63989.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2g0ha8\\cmTC_63989.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.77
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:250 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:557 (_OPENMP_GET_FLAGS)"
      - "CMakeLists.txt:32 (find_package)"
    description: "Detecting CXX OpenMP compiler info"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-sl6ak3"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-sl6ak3"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "OpenMP_COMPILE_RESULT_CXX_openmp"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-sl6ak3'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_7b13a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:53.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\cmTC_7b13a.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_7b13a.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\Debug\\".
          Creazione directory "cmTC_7b13a.dir\\Debug\\cmTC_7b13a.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_7b13a.dir\\Debug\\cmTC_7b13a.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_7b13a.dir\\Debug\\\\" /Fd"cmTC_7b13a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\OpenMPTryFlag.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          OpenMPTryFlag.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_7b13a.dir\\Debug\\\\" /Fd"cmTC_7b13a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\OpenMPTryFlag.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\Debug\\cmTC_7b13a.exe" /INCREMENTAL /ILK:"cmTC_7b13a.dir\\Debug\\cmTC_7b13a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-sl6ak3/Debug/cmTC_7b13a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-sl6ak3/Debug/cmTC_7b13a.lib" /MACHINE:X64  /machine:x64 cmTC_7b13a.dir\\Debug\\OpenMPTryFlag.obj
          cmTC_7b13a.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\Debug\\cmTC_7b13a.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_7b13a.dir\\Debug\\cmTC_7b13a.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_7b13a.dir\\Debug\\cmTC_7b13a.tlog\\cmTC_7b13a.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sl6ak3\\cmTC_7b13a.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.70
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:491 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:631 (_OPENMP_GET_SPEC_DATE)"
      - "CMakeLists.txt:32 (find_package)"
    description: "Detecting C OpenMP version"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-oye1xa"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-oye1xa"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "OpenMP_SPECTEST_C_"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-oye1xa'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_7a71c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:54.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\cmTC_7a71c.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_7a71c.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\Debug\\".
          Creazione directory "cmTC_7a71c.dir\\Debug\\cmTC_7a71c.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_7a71c.dir\\Debug\\cmTC_7a71c.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /Fo"cmTC_7a71c.dir\\Debug\\\\" /Fd"cmTC_7a71c.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\OpenMPCheckVersion.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          OpenMPCheckVersion.c
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /Fo"cmTC_7a71c.dir\\Debug\\\\" /Fd"cmTC_7a71c.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\OpenMPCheckVersion.c"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\Debug\\cmTC_7a71c.exe" /INCREMENTAL /ILK:"cmTC_7a71c.dir\\Debug\\cmTC_7a71c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-oye1xa/Debug/cmTC_7a71c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-oye1xa/Debug/cmTC_7a71c.lib" /MACHINE:X64  /machine:x64 cmTC_7a71c.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_7a71c.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\Debug\\cmTC_7a71c.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_7a71c.dir\\Debug\\cmTC_7a71c.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_7a71c.dir\\Debug\\cmTC_7a71c.tlog\\cmTC_7a71c.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oye1xa\\cmTC_7a71c.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:491 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindOpenMP.cmake:631 (_OPENMP_GET_SPEC_DATE)"
      - "CMakeLists.txt:32 (find_package)"
    description: "Detecting CXX OpenMP version"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2apg8t"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2apg8t"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "OpenMP_SPECTEST_CXX_"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2apg8t'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_d956f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:55.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\cmTC_d956f.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_d956f.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\Debug\\".
          Creazione directory "cmTC_d956f.dir\\Debug\\cmTC_d956f.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_d956f.dir\\Debug\\cmTC_d956f.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_d956f.dir\\Debug\\\\" /Fd"cmTC_d956f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\OpenMPCheckVersion.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          OpenMPCheckVersion.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_d956f.dir\\Debug\\\\" /Fd"cmTC_d956f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\OpenMPCheckVersion.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\Debug\\cmTC_d956f.exe" /INCREMENTAL /ILK:"cmTC_d956f.dir\\Debug\\cmTC_d956f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2apg8t/Debug/cmTC_d956f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-2apg8t/Debug/cmTC_d956f.lib" /MACHINE:X64  /machine:x64 cmTC_d956f.dir\\Debug\\OpenMPCheckVersion.obj
          cmTC_d956f.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\Debug\\cmTC_d956f.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_d956f.dir\\Debug\\cmTC_d956f.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_d956f.dir\\Debug\\cmTC_d956f.tlog\\cmTC_d956f.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2apg8t\\cmTC_d956f.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.74
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckIPOSupported.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckIPOSupported.cmake:277 (_ipo_run_language_check)"
      - "build/_deps/tbb-src/CMakeLists.txt:217 (check_ipo_supported)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/src"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/bin'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" ALL_BUILD.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:58.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ALL_BUILD.vcxproj" sul nodo 1 (destinazioni predefinite).
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ZERO_CHECK.vcxproj" (2) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ALL_BUILD.vcxproj" (1) sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "x64\\Debug\\ZERO_CHECK\\".
          Creazione directory "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\".
        InitializeBuildStatus:
          Creazione di "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          1>Checking Build System
        FinalizeBuildStatus:
          Eliminazione del file "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\ZERO_CHECK.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ZERO_CHECK.vcxproj" (destinazioni predefinite) completata.
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\boo.vcxproj" (3) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ALL_BUILD.vcxproj" (1) sul nodo 1 (destinazioni predefinite).
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\foo.vcxproj" (4) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\boo.vcxproj" (3) sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "foo.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\".
          Creazione directory "foo.dir\\Debug\\foo.tlog\\".
        InitializeBuildStatus:
          Creazione di "foo.dir\\Debug\\foo.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"foo.dir\\Debug\\\\" /Fd"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\foo.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          foo.cpp
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"foo.dir\\Debug\\\\" /Fd"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\foo.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\foo.cpp"
        Lib:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\Lib.exe /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\foo.lib" /NOLOGO /MACHINE:X64 /LTCG  /machine:x64 foo.dir\\Debug\\foo.obj
          foo.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\foo.lib
        FinalizeBuildStatus:
          Eliminazione del file "foo.dir\\Debug\\foo.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "foo.dir\\Debug\\foo.tlog\\foo.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\foo.vcxproj" (destinazioni predefinite) completata.
        PrepareForBuild:
          Creazione directory "boo.dir\\Debug\\".
          Creazione directory "boo.dir\\Debug\\boo.tlog\\".
        InitializeBuildStatus:
          Creazione di "boo.dir\\Debug\\boo.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"boo.dir\\Debug\\\\" /Fd"boo.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"boo.dir\\Debug\\\\" /Fd"boo.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\src\\main.cpp"
          main.cpp
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\boo.exe" /NOLOGO Debug\\foo.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/bin/Debug/boo.pdb" /SUBSYSTEM:CONSOLE /LTCG:incremental /LTCGOUT:"boo.dir\\Debug\\boo.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/bin/Debug/boo.lib" /MACHINE:X64  /machine:x64 boo.dir\\Debug\\main.obj
          Generazione codice in corso...
          Not all modules are compiled with -Gy (function comdat), build without incremental LTCG.
          Generazione codice terminata
          boo.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\Debug\\boo.exe
        FinalizeBuildStatus:
          Eliminazione del file "boo.dir\\Debug\\boo.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "boo.dir\\Debug\\boo.tlog\\boo.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\boo.vcxproj" (destinazioni predefinite) completata.
        PrepareForBuild:
          Creazione directory "x64\\Debug\\ALL_BUILD\\".
          Creazione directory "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\".
        InitializeBuildStatus:
          Creazione di "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-CXX/src/CMakeLists.txt
        FinalizeBuildStatus:
          Eliminazione del file "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\ALL_BUILD.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-CXX\\bin\\ALL_BUILD.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:01.31
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckIPOSupported.cmake:154 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckIPOSupported.cmake:277 (_ipo_run_language_check)"
      - "build/_deps/tbb-src/CMakeLists.txt:217 (check_ipo_supported)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/src"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/bin"
    buildResult:
      variable: "_IPO_LANGUAGE_CHECK_RESULT"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/bin'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" ALL_BUILD.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:44:59.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ALL_BUILD.vcxproj" sul nodo 1 (destinazioni predefinite).
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ZERO_CHECK.vcxproj" (2) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ALL_BUILD.vcxproj" (1) sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "x64\\Debug\\ZERO_CHECK\\".
          Creazione directory "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\".
        InitializeBuildStatus:
          Creazione di "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          1>Checking Build System
        FinalizeBuildStatus:
          Eliminazione del file "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "x64\\Debug\\ZERO_CHECK\\ZERO_CHECK.tlog\\ZERO_CHECK.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ZERO_CHECK.vcxproj" (destinazioni predefinite) completata.
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\boo.vcxproj" (3) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ALL_BUILD.vcxproj" (1) sul nodo 1 (destinazioni predefinite).
        Compilazione di "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\foo.vcxproj" (4) dal progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\boo.vcxproj" (3) sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "foo.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\".
          Creazione directory "foo.dir\\Debug\\foo.tlog\\".
        InitializeBuildStatus:
          Creazione di "foo.dir\\Debug\\foo.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/src/CMakeLists.txt
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"foo.dir\\Debug\\\\" /Fd"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\foo.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          foo.c
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"foo.dir\\Debug\\\\" /Fd"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\foo.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\src\\foo.c"
        Lib:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\Lib.exe /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\foo.lib" /NOLOGO /MACHINE:X64 /LTCG  /machine:x64 foo.dir\\Debug\\foo.obj
          foo.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\foo.lib
        FinalizeBuildStatus:
          Eliminazione del file "foo.dir\\Debug\\foo.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "foo.dir\\Debug\\foo.tlog\\foo.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\foo.vcxproj" (destinazioni predefinite) completata.
        PrepareForBuild:
          Creazione directory "boo.dir\\Debug\\".
          Creazione directory "boo.dir\\Debug\\boo.tlog\\".
        InitializeBuildStatus:
          Creazione di "boo.dir\\Debug\\boo.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/src/CMakeLists.txt
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"boo.dir\\Debug\\\\" /Fd"boo.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /GL /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"boo.dir\\Debug\\\\" /Fd"boo.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\src\\main.c"
          main.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\boo.exe" /NOLOGO Debug\\foo.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/bin/Debug/boo.pdb" /SUBSYSTEM:CONSOLE /LTCG:incremental /LTCGOUT:"boo.dir\\Debug\\boo.iobj" /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/bin/Debug/boo.lib" /MACHINE:X64  /machine:x64 boo.dir\\Debug\\main.obj
          Generazione codice in corso...
          Not all modules are compiled with -Gy (function comdat), build without incremental LTCG.
          Generazione codice terminata
          boo.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\Debug\\boo.exe
        FinalizeBuildStatus:
          Eliminazione del file "boo.dir\\Debug\\boo.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "boo.dir\\Debug\\boo.tlog\\boo.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\boo.vcxproj" (destinazioni predefinite) completata.
        PrepareForBuild:
          Creazione directory "x64\\Debug\\ALL_BUILD\\".
          Creazione directory "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\".
        InitializeBuildStatus:
          Creazione di "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        CustomBuild:
          Building Custom Rule C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build/CMakeFiles/_CMakeLTOTest-C/src/CMakeLists.txt
        FinalizeBuildStatus:
          Eliminazione del file "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "x64\\Debug\\ALL_BUILD\\ALL_BUILD.tlog\\ALL_BUILD.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\tbb-build\\CMakeFiles\\_CMakeLTOTest-C\\bin\\ALL_BUILD.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:01.26
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/embree-src/CMakeLists.txt:298 (check_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_HAS_SYCL_SUPPORT"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-9ulxy5"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-9ulxy5"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
    buildResult:
      variable: "COMPILER_HAS_SYCL_SUPPORT"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-9ulxy5'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_910c6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:04.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\cmTC_910c6.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_910c6.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\Debug\\".
          Creazione directory "cmTC_910c6.dir\\Debug\\cmTC_910c6.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_910c6.dir\\Debug\\cmTC_910c6.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_HAS_SYCL_SUPPORT /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_910c6.dir\\Debug\\\\" /Fd"cmTC_910c6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -fsycl "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_HAS_SYCL_SUPPORT /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_910c6.dir\\Debug\\\\" /Fd"cmTC_910c6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -fsycl "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-fsycl' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\Debug\\cmTC_910c6.exe" /INCREMENTAL /ILK:"cmTC_910c6.dir\\Debug\\cmTC_910c6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-9ulxy5/Debug/cmTC_910c6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-9ulxy5/Debug/cmTC_910c6.lib" /MACHINE:X64  /machine:x64 cmTC_910c6.dir\\Debug\\src.obj
          cmTC_910c6.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\Debug\\cmTC_910c6.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_910c6.dir\\Debug\\cmTC_910c6.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_910c6.dir\\Debug\\cmTC_910c6.tlog\\cmTC_910c6.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-9ulxy5\\cmTC_910c6.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.81
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:403 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c39db.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:05.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_c39db.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_c39db.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_c39db.dir\\Debug\\cmTC_c39db.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_c39db.dir\\Debug\\cmTC_c39db.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c39db.dir\\Debug\\\\" /Fd"cmTC_c39db.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c39db.dir\\Debug\\\\" /Fd"cmTC_c39db.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c39db.exe" /INCREMENTAL /ILK:"cmTC_c39db.dir\\Debug\\cmTC_c39db.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_c39db.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_c39db.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_c39db.dir\\Debug\\check_isa.obj
          cmTC_c39db.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_c39db.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_c39db.dir\\Debug\\cmTC_c39db.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_c39db.dir\\Debug\\cmTC_c39db.tlog\\cmTC_c39db.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_c39db.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.84
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:404 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_f641a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:06.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_f641a.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_f641a.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_f641a.dir\\Debug\\cmTC_f641a.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_f641a.dir\\Debug\\cmTC_f641a.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f641a.dir\\Debug\\\\" /Fd"cmTC_f641a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f641a.dir\\Debug\\\\" /Fd"cmTC_f641a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_f641a.exe" /INCREMENTAL /ILK:"cmTC_f641a.dir\\Debug\\cmTC_f641a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_f641a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_f641a.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_f641a.dir\\Debug\\check_isa.obj
          cmTC_f641a.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_f641a.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_f641a.dir\\Debug\\cmTC_f641a.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_f641a.dir\\Debug\\cmTC_f641a.tlog\\cmTC_f641a.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_f641a.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.75
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:405 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_e474a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:07.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_e474a.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_e474a.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_e474a.dir\\Debug\\cmTC_e474a.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_e474a.dir\\Debug\\cmTC_e474a.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e474a.dir\\Debug\\\\" /Fd"cmTC_e474a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e474a.dir\\Debug\\\\" /Fd"cmTC_e474a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_e474a.exe" /INCREMENTAL /ILK:"cmTC_e474a.dir\\Debug\\cmTC_e474a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_e474a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_e474a.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_e474a.dir\\Debug\\check_isa.obj
          cmTC_e474a.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_e474a.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_e474a.dir\\Debug\\cmTC_e474a.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_e474a.dir\\Debug\\cmTC_e474a.tlog\\cmTC_e474a.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_e474a.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.76
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:77 (check_cxx_compiler_flag)"
    checks:
      - "Performing Test EIGEN_COMPILER_SUPPORT_CPP11"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-u7rkgz"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-u7rkgz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "EIGEN_COMPILER_SUPPORT_CPP11"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-u7rkgz'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c2d70.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:14.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\cmTC_c2d70.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_c2d70.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\Debug\\".
          Creazione directory "cmTC_c2d70.dir\\Debug\\cmTC_c2d70.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_c2d70.dir\\Debug\\cmTC_c2d70.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D EIGEN_COMPILER_SUPPORT_CPP11 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c2d70.dir\\Debug\\\\" /Fd"cmTC_c2d70.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++11 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D EIGEN_COMPILER_SUPPORT_CPP11 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c2d70.dir\\Debug\\\\" /Fd"cmTC_c2d70.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++11 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++11' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\Debug\\cmTC_c2d70.exe" /INCREMENTAL /ILK:"cmTC_c2d70.dir\\Debug\\cmTC_c2d70.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-u7rkgz/Debug/cmTC_c2d70.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-u7rkgz/Debug/cmTC_c2d70.lib" /MACHINE:X64  /machine:x64 cmTC_c2d70.dir\\Debug\\src.obj
          cmTC_c2d70.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\Debug\\cmTC_c2d70.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_c2d70.dir\\Debug\\cmTC_c2d70.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_c2d70.dir\\Debug\\cmTC_c2d70.tlog\\cmTC_c2d70.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-u7rkgz\\cmTC_c2d70.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:71 (check_cxx_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:88 (ei_add_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_std=cpp03"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-3k66km"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-3k66km"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "COMPILER_SUPPORT_std=cpp03"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-3k66km'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_b6fee.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:15.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\cmTC_b6fee.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_b6fee.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\Debug\\".
          Creazione directory "cmTC_b6fee.dir\\Debug\\cmTC_b6fee.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_b6fee.dir\\Debug\\cmTC_b6fee.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_b6fee.dir\\Debug\\\\" /Fd"cmTC_b6fee.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_b6fee.dir\\Debug\\\\" /Fd"cmTC_b6fee.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\Debug\\cmTC_b6fee.exe" /INCREMENTAL /ILK:"cmTC_b6fee.dir\\Debug\\cmTC_b6fee.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-3k66km/Debug/cmTC_b6fee.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-3k66km/Debug/cmTC_b6fee.lib" /MACHINE:X64  /machine:x64 cmTC_b6fee.dir\\Debug\\src.obj
          cmTC_b6fee.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\Debug\\cmTC_b6fee.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_b6fee.dir\\Debug\\cmTC_b6fee.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_b6fee.dir\\Debug\\cmTC_b6fee.tlog\\cmTC_b6fee.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3k66km\\cmTC_b6fee.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.68
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "build/_deps/eigen3-src/cmake/FindStandardMathLibrary.cmake:35 (CHECK_CXX_SOURCE_COMPILES)"
      - "build/_deps/eigen3-src/CMakeLists.txt:98 (find_package)"
    checks:
      - "Performing Test standard_math_library_linked_to_automatically"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-eqxbwg"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-eqxbwg"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "standard_math_library_linked_to_automatically"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-eqxbwg'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_00804.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:16.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\cmTC_00804.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_00804.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\Debug\\".
          Creazione directory "cmTC_00804.dir\\Debug\\cmTC_00804.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_00804.dir\\Debug\\cmTC_00804.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D standard_math_library_linked_to_automatically /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_00804.dir\\Debug\\\\" /Fd"cmTC_00804.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D standard_math_library_linked_to_automatically /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_00804.dir\\Debug\\\\" /Fd"cmTC_00804.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\Debug\\cmTC_00804.exe" /INCREMENTAL /ILK:"cmTC_00804.dir\\Debug\\cmTC_00804.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-eqxbwg/Debug/cmTC_00804.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-eqxbwg/Debug/cmTC_00804.lib" /MACHINE:X64  /machine:x64 cmTC_00804.dir\\Debug\\src.obj
          cmTC_00804.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\Debug\\cmTC_00804.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_00804.dir\\Debug\\cmTC_00804.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_00804.dir\\Debug\\cmTC_00804.tlog\\cmTC_00804.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-eqxbwg\\cmTC_00804.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.70
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:347 (check_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_OPENMP"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-uhw001"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-uhw001"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "COMPILER_SUPPORT_OPENMP"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-uhw001'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_905f5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:17.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\cmTC_905f5.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_905f5.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\Debug\\".
          Creazione directory "cmTC_905f5.dir\\Debug\\cmTC_905f5.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_905f5.dir\\Debug\\cmTC_905f5.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_OPENMP /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_905f5.dir\\Debug\\\\" /Fd"cmTC_905f5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_OPENMP /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /openmp /std:c++17 /Fo"cmTC_905f5.dir\\Debug\\\\" /Fd"cmTC_905f5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\Debug\\cmTC_905f5.exe" /INCREMENTAL /ILK:"cmTC_905f5.dir\\Debug\\cmTC_905f5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-uhw001/Debug/cmTC_905f5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-uhw001/Debug/cmTC_905f5.lib" /MACHINE:X64  /machine:x64 cmTC_905f5.dir\\Debug\\src.obj
          cmTC_905f5.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\Debug\\cmTC_905f5.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_905f5.dir\\Debug\\cmTC_905f5.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_905f5.dir\\Debug\\cmTC_905f5.tlog\\cmTC_905f5.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-uhw001\\cmTC_905f5.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:108 (try_compile)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:8 (_DetermineVSServicePack_CheckVersionWithTryCompile)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-r97znp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-r97znp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-r97znp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_6191c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:20.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-r97znp\\cmTC_6191c.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_6191c.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-r97znp\\Debug\\".
          Creazione directory "cmTC_6191c.dir\\Debug\\cmTC_6191c.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_6191c.dir\\Debug\\cmTC_6191c.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6191c.dir\\Debug\\\\" /Fd"cmTC_6191c.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6191c.dir\\Debug\\\\" /Fd"cmTC_6191c.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-r97znp\\Debug\\cmTC_6191c.exe" /INCREMENTAL /ILK:"cmTC_6191c.dir\\Debug\\cmTC_6191c.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-r97znp/Debug/cmTC_6191c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-r97znp/Debug/cmTC_6191c.lib" /MACHINE:X64  /machine:x64 cmTC_6191c.dir\\Debug\\return0.obj
          cmTC_6191c.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-r97znp\\Debug\\cmTC_6191c.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_6191c.dir\\Debug\\cmTC_6191c.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_6191c.dir\\Debug\\cmTC_6191c.tlog\\cmTC_6191c.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-r97znp\\cmTC_6191c.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.73
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:129 (try_run)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:10 (_DetermineVSServicePack_CheckVersionWithTryRun)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-howzr9"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-howzr9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-howzr9'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_68989.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:21.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-howzr9\\cmTC_68989.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_68989.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-howzr9\\Debug\\".
          Creazione directory "cmTC_68989.dir\\Debug\\cmTC_68989.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_68989.dir\\Debug\\cmTC_68989.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_68989.dir\\Debug\\\\" /Fd"cmTC_68989.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_68989.dir\\Debug\\\\" /Fd"cmTC_68989.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-howzr9\\Debug\\cmTC_68989.exe" /INCREMENTAL /ILK:"cmTC_68989.dir\\Debug\\cmTC_68989.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-howzr9/Debug/cmTC_68989.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-howzr9/Debug/cmTC_68989.lib" /MACHINE:X64  /machine:x64 cmTC_68989.dir\\Debug\\return0.obj
          cmTC_68989.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-howzr9\\Debug\\cmTC_68989.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_68989.dir\\Debug\\cmTC_68989.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_68989.dir\\Debug\\cmTC_68989.tlog\\cmTC_68989.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-howzr9\\cmTC_68989.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.70
        
      exitCode: 0
    runResult:
      variable: "_RunResult"
      cached: true
      stdout: |
        19.29.30159.01
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLanguage.cmake:140 (message)"
      - "build/_deps/eigen3-src/test/CMakeLists.txt:9 (check_language)"
    checks:
      - "Looking for a Fortran compiler"
    message: |
      Looking for a Fortran compiler failed with the following output:
      -- Selecting Windows SDK version 10.0.19041.0 to target Windows 10.0.26100.
      -- The Fortran compiler identification is unknown
      CMake Error at CMakeLists.txt:3 (project):
        No CMAKE_Fortran_COMPILER could be found.
      
      
      
      -- Configuring incomplete, errors occurred!
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/test/CMakeLists.txt:295 (check_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_FASTMATH"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-y7xw3o"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-y7xw3o"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714 /D_CRT_SECURE_NO_WARNINGS /D_SCL_SECURE_NO_WARNINGS"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
    buildResult:
      variable: "COMPILER_SUPPORT_FASTMATH"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-y7xw3o'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_23d45.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:23.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\cmTC_23d45.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_23d45.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\Debug\\".
          Creazione directory "cmTC_23d45.dir\\Debug\\cmTC_23d45.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_23d45.dir\\Debug\\cmTC_23d45.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _CRT_SECURE_NO_WARNINGS /D _SCL_SECURE_NO_WARNINGS /D COMPILER_SUPPORT_FASTMATH /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_23d45.dir\\Debug\\\\" /Fd"cmTC_23d45.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 -ffast-math "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _CRT_SECURE_NO_WARNINGS /D _SCL_SECURE_NO_WARNINGS /D COMPILER_SUPPORT_FASTMATH /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_23d45.dir\\Debug\\\\" /Fd"cmTC_23d45.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 -ffast-math "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-ffast-math' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\Debug\\cmTC_23d45.exe" /INCREMENTAL /ILK:"cmTC_23d45.dir\\Debug\\cmTC_23d45.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-y7xw3o/Debug/cmTC_23d45.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-y7xw3o/Debug/cmTC_23d45.lib" /MACHINE:X64  /machine:x64 cmTC_23d45.dir\\Debug\\src.obj
          cmTC_23d45.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\Debug\\cmTC_23d45.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_23d45.dir\\Debug\\cmTC_23d45.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_23d45.dir\\Debug\\cmTC_23d45.tlog\\cmTC_23d45.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y7xw3o\\cmTC_23d45.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.74
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:403 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_0e651.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:56.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_0e651.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_0e651.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_0e651.dir\\Debug\\cmTC_0e651.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_0e651.dir\\Debug\\cmTC_0e651.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0e651.dir\\Debug\\\\" /Fd"cmTC_0e651.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0e651.dir\\Debug\\\\" /Fd"cmTC_0e651.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_0e651.exe" /INCREMENTAL /ILK:"cmTC_0e651.dir\\Debug\\cmTC_0e651.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_0e651.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_0e651.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_0e651.dir\\Debug\\check_isa.obj
          cmTC_0e651.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_0e651.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_0e651.dir\\Debug\\cmTC_0e651.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_0e651.dir\\Debug\\cmTC_0e651.tlog\\cmTC_0e651.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_0e651.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.83
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:404 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_8fbf0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:57.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_8fbf0.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_8fbf0.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_8fbf0.dir\\Debug\\cmTC_8fbf0.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_8fbf0.dir\\Debug\\cmTC_8fbf0.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8fbf0.dir\\Debug\\\\" /Fd"cmTC_8fbf0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8fbf0.dir\\Debug\\\\" /Fd"cmTC_8fbf0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_8fbf0.exe" /INCREMENTAL /ILK:"cmTC_8fbf0.dir\\Debug\\cmTC_8fbf0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_8fbf0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_8fbf0.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_8fbf0.dir\\Debug\\check_isa.obj
          cmTC_8fbf0.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_8fbf0.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_8fbf0.dir\\Debug\\cmTC_8fbf0.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_8fbf0.dir\\Debug\\cmTC_8fbf0.tlog\\cmTC_8fbf0.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_8fbf0.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.68
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:405 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_bd722.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:45:58.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_bd722.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_bd722.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_bd722.dir\\Debug\\cmTC_bd722.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_bd722.dir\\Debug\\cmTC_bd722.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bd722.dir\\Debug\\\\" /Fd"cmTC_bd722.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bd722.dir\\Debug\\\\" /Fd"cmTC_bd722.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_bd722.exe" /INCREMENTAL /ILK:"cmTC_bd722.dir\\Debug\\cmTC_bd722.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_bd722.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_bd722.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_bd722.dir\\Debug\\check_isa.obj
          cmTC_bd722.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_bd722.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_bd722.dir\\Debug\\cmTC_bd722.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_bd722.dir\\Debug\\cmTC_bd722.tlog\\cmTC_bd722.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_bd722.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.75
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:71 (check_cxx_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:88 (ei_add_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_std=cpp03"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-l8prm9"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-l8prm9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORT_std=cpp03"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-l8prm9'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c8033.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:06.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\cmTC_c8033.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_c8033.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\Debug\\".
          Creazione directory "cmTC_c8033.dir\\Debug\\cmTC_c8033.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_c8033.dir\\Debug\\cmTC_c8033.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c8033.dir\\Debug\\\\" /Fd"cmTC_c8033.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c8033.dir\\Debug\\\\" /Fd"cmTC_c8033.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\Debug\\cmTC_c8033.exe" /INCREMENTAL /ILK:"cmTC_c8033.dir\\Debug\\cmTC_c8033.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-l8prm9/Debug/cmTC_c8033.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-l8prm9/Debug/cmTC_c8033.lib" /MACHINE:X64  /machine:x64 cmTC_c8033.dir\\Debug\\src.obj
          cmTC_c8033.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\Debug\\cmTC_c8033.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_c8033.dir\\Debug\\cmTC_c8033.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_c8033.dir\\Debug\\cmTC_c8033.tlog\\cmTC_c8033.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l8prm9\\cmTC_c8033.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.80
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:108 (try_compile)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:8 (_DetermineVSServicePack_CheckVersionWithTryCompile)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1sj9d6"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1sj9d6"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1sj9d6'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_dda74.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:08.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1sj9d6\\cmTC_dda74.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_dda74.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1sj9d6\\Debug\\".
          Creazione directory "cmTC_dda74.dir\\Debug\\cmTC_dda74.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_dda74.dir\\Debug\\cmTC_dda74.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_dda74.dir\\Debug\\\\" /Fd"cmTC_dda74.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_dda74.dir\\Debug\\\\" /Fd"cmTC_dda74.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1sj9d6\\Debug\\cmTC_dda74.exe" /INCREMENTAL /ILK:"cmTC_dda74.dir\\Debug\\cmTC_dda74.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1sj9d6/Debug/cmTC_dda74.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1sj9d6/Debug/cmTC_dda74.lib" /MACHINE:X64  /machine:x64 cmTC_dda74.dir\\Debug\\return0.obj
          cmTC_dda74.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1sj9d6\\Debug\\cmTC_dda74.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_dda74.dir\\Debug\\cmTC_dda74.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_dda74.dir\\Debug\\cmTC_dda74.tlog\\cmTC_dda74.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1sj9d6\\cmTC_dda74.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.82
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:129 (try_run)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:10 (_DetermineVSServicePack_CheckVersionWithTryRun)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-nczon5"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-nczon5"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-nczon5'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_c47fd.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:09.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nczon5\\cmTC_c47fd.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_c47fd.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nczon5\\Debug\\".
          Creazione directory "cmTC_c47fd.dir\\Debug\\cmTC_c47fd.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_c47fd.dir\\Debug\\cmTC_c47fd.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c47fd.dir\\Debug\\\\" /Fd"cmTC_c47fd.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c47fd.dir\\Debug\\\\" /Fd"cmTC_c47fd.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nczon5\\Debug\\cmTC_c47fd.exe" /INCREMENTAL /ILK:"cmTC_c47fd.dir\\Debug\\cmTC_c47fd.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-nczon5/Debug/cmTC_c47fd.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-nczon5/Debug/cmTC_c47fd.lib" /MACHINE:X64  /machine:x64 cmTC_c47fd.dir\\Debug\\return0.obj
          cmTC_c47fd.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nczon5\\Debug\\cmTC_c47fd.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_c47fd.dir\\Debug\\cmTC_c47fd.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_c47fd.dir\\Debug\\cmTC_c47fd.tlog\\cmTC_c47fd.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nczon5\\cmTC_c47fd.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.79
        
      exitCode: 0
    runResult:
      variable: "_RunResult"
      cached: true
      stdout: |
        19.29.30159.01
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:403 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_cd7ff.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:52.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_cd7ff.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_cd7ff.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_cd7ff.dir\\Debug\\cmTC_cd7ff.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_cd7ff.dir\\Debug\\cmTC_cd7ff.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cd7ff.dir\\Debug\\\\" /Fd"cmTC_cd7ff.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_cd7ff.dir\\Debug\\\\" /Fd"cmTC_cd7ff.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_cd7ff.exe" /INCREMENTAL /ILK:"cmTC_cd7ff.dir\\Debug\\cmTC_cd7ff.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_cd7ff.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_cd7ff.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_cd7ff.dir\\Debug\\check_isa.obj
          cmTC_cd7ff.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_cd7ff.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_cd7ff.dir\\Debug\\cmTC_cd7ff.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_cd7ff.dir\\Debug\\cmTC_cd7ff.tlog\\cmTC_cd7ff.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_cd7ff.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.85
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:404 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_7d80f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:53.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_7d80f.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_7d80f.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_7d80f.dir\\Debug\\cmTC_7d80f.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_7d80f.dir\\Debug\\cmTC_7d80f.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7d80f.dir\\Debug\\\\" /Fd"cmTC_7d80f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7d80f.dir\\Debug\\\\" /Fd"cmTC_7d80f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_7d80f.exe" /INCREMENTAL /ILK:"cmTC_7d80f.dir\\Debug\\cmTC_7d80f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_7d80f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_7d80f.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_7d80f.dir\\Debug\\check_isa.obj
          cmTC_7d80f.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_7d80f.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_7d80f.dir\\Debug\\cmTC_7d80f.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_7d80f.dir\\Debug\\cmTC_7d80f.tlog\\cmTC_7d80f.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_7d80f.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:405 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_3b5e5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:54.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_3b5e5.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_3b5e5.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_3b5e5.dir\\Debug\\cmTC_3b5e5.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_3b5e5.dir\\Debug\\cmTC_3b5e5.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3b5e5.dir\\Debug\\\\" /Fd"cmTC_3b5e5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3b5e5.dir\\Debug\\\\" /Fd"cmTC_3b5e5.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_3b5e5.exe" /INCREMENTAL /ILK:"cmTC_3b5e5.dir\\Debug\\cmTC_3b5e5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_3b5e5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_3b5e5.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_3b5e5.dir\\Debug\\check_isa.obj
          cmTC_3b5e5.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_3b5e5.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_3b5e5.dir\\Debug\\cmTC_3b5e5.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_3b5e5.dir\\Debug\\cmTC_3b5e5.tlog\\cmTC_3b5e5.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_3b5e5.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:71 (check_cxx_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:88 (ei_add_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_std=cpp03"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-fsbmak"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-fsbmak"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORT_std=cpp03"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-fsbmak'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_8146a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:46:59.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\cmTC_8146a.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_8146a.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\Debug\\".
          Creazione directory "cmTC_8146a.dir\\Debug\\cmTC_8146a.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_8146a.dir\\Debug\\cmTC_8146a.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_8146a.dir\\Debug\\\\" /Fd"cmTC_8146a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_8146a.dir\\Debug\\\\" /Fd"cmTC_8146a.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\Debug\\cmTC_8146a.exe" /INCREMENTAL /ILK:"cmTC_8146a.dir\\Debug\\cmTC_8146a.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-fsbmak/Debug/cmTC_8146a.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-fsbmak/Debug/cmTC_8146a.lib" /MACHINE:X64  /machine:x64 cmTC_8146a.dir\\Debug\\src.obj
          cmTC_8146a.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\Debug\\cmTC_8146a.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_8146a.dir\\Debug\\cmTC_8146a.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_8146a.dir\\Debug\\cmTC_8146a.tlog\\cmTC_8146a.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fsbmak\\cmTC_8146a.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:108 (try_compile)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:8 (_DetermineVSServicePack_CheckVersionWithTryCompile)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8e7hsz"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8e7hsz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8e7hsz'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_9beba.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:01.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8e7hsz\\cmTC_9beba.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_9beba.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8e7hsz\\Debug\\".
          Creazione directory "cmTC_9beba.dir\\Debug\\cmTC_9beba.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_9beba.dir\\Debug\\cmTC_9beba.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9beba.dir\\Debug\\\\" /Fd"cmTC_9beba.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_9beba.dir\\Debug\\\\" /Fd"cmTC_9beba.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8e7hsz\\Debug\\cmTC_9beba.exe" /INCREMENTAL /ILK:"cmTC_9beba.dir\\Debug\\cmTC_9beba.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8e7hsz/Debug/cmTC_9beba.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8e7hsz/Debug/cmTC_9beba.lib" /MACHINE:X64  /machine:x64 cmTC_9beba.dir\\Debug\\return0.obj
          cmTC_9beba.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8e7hsz\\Debug\\cmTC_9beba.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_9beba.dir\\Debug\\cmTC_9beba.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_9beba.dir\\Debug\\cmTC_9beba.tlog\\cmTC_9beba.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8e7hsz\\cmTC_9beba.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.69
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:129 (try_run)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:10 (_DetermineVSServicePack_CheckVersionWithTryRun)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8l3ybj"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8l3ybj"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8l3ybj'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_6d0b2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:02.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8l3ybj\\cmTC_6d0b2.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_6d0b2.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8l3ybj\\Debug\\".
          Creazione directory "cmTC_6d0b2.dir\\Debug\\cmTC_6d0b2.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_6d0b2.dir\\Debug\\cmTC_6d0b2.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6d0b2.dir\\Debug\\\\" /Fd"cmTC_6d0b2.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6d0b2.dir\\Debug\\\\" /Fd"cmTC_6d0b2.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8l3ybj\\Debug\\cmTC_6d0b2.exe" /INCREMENTAL /ILK:"cmTC_6d0b2.dir\\Debug\\cmTC_6d0b2.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8l3ybj/Debug/cmTC_6d0b2.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8l3ybj/Debug/cmTC_6d0b2.lib" /MACHINE:X64  /machine:x64 cmTC_6d0b2.dir\\Debug\\return0.obj
          cmTC_6d0b2.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8l3ybj\\Debug\\cmTC_6d0b2.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_6d0b2.dir\\Debug\\cmTC_6d0b2.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_6d0b2.dir\\Debug\\cmTC_6d0b2.tlog\\cmTC_6d0b2.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8l3ybj\\cmTC_6d0b2.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.68
        
      exitCode: 0
    runResult:
      variable: "_RunResult"
      cached: true
      stdout: |
        19.29.30159.01
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:403 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_fc58d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:19.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_fc58d.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_fc58d.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_fc58d.dir\\Debug\\cmTC_fc58d.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_fc58d.dir\\Debug\\cmTC_fc58d.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fc58d.dir\\Debug\\\\" /Fd"cmTC_fc58d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fc58d.dir\\Debug\\\\" /Fd"cmTC_fc58d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_fc58d.exe" /INCREMENTAL /ILK:"cmTC_fc58d.dir\\Debug\\cmTC_fc58d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_fc58d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_fc58d.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_fc58d.dir\\Debug\\check_isa.obj
          cmTC_fc58d.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_fc58d.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_fc58d.dir\\Debug\\cmTC_fc58d.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_fc58d.dir\\Debug\\cmTC_fc58d.tlog\\cmTC_fc58d.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_fc58d.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.74
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:404 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_d13a6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:20.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_d13a6.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_d13a6.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_d13a6.dir\\Debug\\cmTC_d13a6.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_d13a6.dir\\Debug\\cmTC_d13a6.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d13a6.dir\\Debug\\\\" /Fd"cmTC_d13a6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_d13a6.dir\\Debug\\\\" /Fd"cmTC_d13a6.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_d13a6.exe" /INCREMENTAL /ILK:"cmTC_d13a6.dir\\Debug\\cmTC_d13a6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_d13a6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_d13a6.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_d13a6.dir\\Debug\\check_isa.obj
          cmTC_d13a6.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_d13a6.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_d13a6.dir\\Debug\\cmTC_d13a6.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_d13a6.dir\\Debug\\cmTC_d13a6.tlog\\cmTC_d13a6.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_d13a6.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.65
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:405 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_acfe2.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:21.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_acfe2.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_acfe2.dir\\Debug\\cmTC_acfe2.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_acfe2.dir\\Debug\\cmTC_acfe2.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_acfe2.dir\\Debug\\\\" /Fd"cmTC_acfe2.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_acfe2.dir\\Debug\\\\" /Fd"cmTC_acfe2.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp : fatal error C1090: Chiamata API PDB non riuscita. Codice errore '3': C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.dir\\Debug\\vc142.pdb [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.vcxproj]
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.vcxproj" (destinazioni predefinite) NON COMPLETATA.
        
        Compilazione NON RIUSCITA.
        
        "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.vcxproj" (destinazione predefinita) (1) ->
        (destinazione: ClCompile) -> 
          C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp : fatal error C1090: Chiamata API PDB non riuscita. Codice errore '3': C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.dir\\Debug\\vc142.pdb [C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_acfe2.vcxproj]
        
            Avvisi: 0
            Errori: 1
        
        Tempo trascorso 00:00:00.52
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:71 (check_cxx_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:88 (ei_add_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_std=cpp03"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pe2kzv"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pe2kzv"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORT_std=cpp03"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pe2kzv'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_432bc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:26.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\cmTC_432bc.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_432bc.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\Debug\\".
          Creazione directory "cmTC_432bc.dir\\Debug\\cmTC_432bc.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_432bc.dir\\Debug\\cmTC_432bc.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_432bc.dir\\Debug\\\\" /Fd"cmTC_432bc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_432bc.dir\\Debug\\\\" /Fd"cmTC_432bc.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\Debug\\cmTC_432bc.exe" /INCREMENTAL /ILK:"cmTC_432bc.dir\\Debug\\cmTC_432bc.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pe2kzv/Debug/cmTC_432bc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-pe2kzv/Debug/cmTC_432bc.lib" /MACHINE:X64  /machine:x64 cmTC_432bc.dir\\Debug\\src.obj
          cmTC_432bc.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\Debug\\cmTC_432bc.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_432bc.dir\\Debug\\cmTC_432bc.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_432bc.dir\\Debug\\cmTC_432bc.tlog\\cmTC_432bc.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pe2kzv\\cmTC_432bc.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.67
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:108 (try_compile)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:8 (_DetermineVSServicePack_CheckVersionWithTryCompile)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ywuj48"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ywuj48"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ywuj48'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_6ce4f.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:28.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywuj48\\cmTC_6ce4f.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_6ce4f.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywuj48\\Debug\\".
          Creazione directory "cmTC_6ce4f.dir\\Debug\\cmTC_6ce4f.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_6ce4f.dir\\Debug\\cmTC_6ce4f.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6ce4f.dir\\Debug\\\\" /Fd"cmTC_6ce4f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_6ce4f.dir\\Debug\\\\" /Fd"cmTC_6ce4f.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywuj48\\Debug\\cmTC_6ce4f.exe" /INCREMENTAL /ILK:"cmTC_6ce4f.dir\\Debug\\cmTC_6ce4f.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ywuj48/Debug/cmTC_6ce4f.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ywuj48/Debug/cmTC_6ce4f.lib" /MACHINE:X64  /machine:x64 cmTC_6ce4f.dir\\Debug\\return0.obj
          cmTC_6ce4f.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywuj48\\Debug\\cmTC_6ce4f.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_6ce4f.dir\\Debug\\cmTC_6ce4f.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_6ce4f.dir\\Debug\\cmTC_6ce4f.tlog\\cmTC_6ce4f.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ywuj48\\cmTC_6ce4f.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.65
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:129 (try_run)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:10 (_DetermineVSServicePack_CheckVersionWithTryRun)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ufbm6x"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ufbm6x"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ufbm6x'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_130d0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:47:29.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ufbm6x\\cmTC_130d0.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_130d0.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ufbm6x\\Debug\\".
          Creazione directory "cmTC_130d0.dir\\Debug\\cmTC_130d0.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_130d0.dir\\Debug\\cmTC_130d0.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_130d0.dir\\Debug\\\\" /Fd"cmTC_130d0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_130d0.dir\\Debug\\\\" /Fd"cmTC_130d0.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ufbm6x\\Debug\\cmTC_130d0.exe" /INCREMENTAL /ILK:"cmTC_130d0.dir\\Debug\\cmTC_130d0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ufbm6x/Debug/cmTC_130d0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-ufbm6x/Debug/cmTC_130d0.lib" /MACHINE:X64  /machine:x64 cmTC_130d0.dir\\Debug\\return0.obj
          cmTC_130d0.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ufbm6x\\Debug\\cmTC_130d0.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_130d0.dir\\Debug\\cmTC_130d0.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_130d0.dir\\Debug\\cmTC_130d0.tlog\\cmTC_130d0.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ufbm6x\\cmTC_130d0.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.67
        
      exitCode: 0
    runResult:
      variable: "_RunResult"
      cached: true
      stdout: |
        19.29.30159.01
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:403 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_5e150.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:50.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_5e150.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_5e150.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_5e150.dir\\Debug\\cmTC_5e150.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_5e150.dir\\Debug\\cmTC_5e150.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e150.dir\\Debug\\\\" /Fd"cmTC_5e150.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e150.dir\\Debug\\\\" /Fd"cmTC_5e150.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_5e150.exe" /INCREMENTAL /ILK:"cmTC_5e150.dir\\Debug\\cmTC_5e150.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_5e150.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_5e150.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_5e150.dir\\Debug\\check_isa.obj
          cmTC_5e150.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_5e150.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_5e150.dir\\Debug\\cmTC_5e150.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_5e150.dir\\Debug\\cmTC_5e150.tlog\\cmTC_5e150.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_5e150.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.75
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:404 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX2"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_717d1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:51.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_717d1.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_717d1.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_717d1.dir\\Debug\\cmTC_717d1.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_717d1.dir\\Debug\\cmTC_717d1.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_717d1.dir\\Debug\\\\" /Fd"cmTC_717d1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D __SSE__ /D __SSE2__ /D __SSE3__ /D __SSSE3__ /D __SSE4_1__ /D __SSE4_2__ /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /arch:AVX2 /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_717d1.dir\\Debug\\\\" /Fd"cmTC_717d1.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_717d1.exe" /INCREMENTAL /ILK:"cmTC_717d1.dir\\Debug\\cmTC_717d1.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_717d1.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_717d1.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_717d1.dir\\Debug\\check_isa.obj
          cmTC_717d1.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_717d1.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_717d1.dir\\Debug\\cmTC_717d1.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_717d1.dir\\Debug\\cmTC_717d1.tlog\\cmTC_717d1.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_717d1.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.65
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "build/_deps/embree-src/CMakeLists.txt:405 (TRY_COMPILE)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64 /DEPENDENTLOADFLAG:0x2000  /NXCompat /DynamicBase"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/common/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORTS_AVX512"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_0c921.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:52.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_0c921.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_0c921.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\".
          Creazione directory "cmTC_0c921.dir\\Debug\\cmTC_0c921.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_0c921.dir\\Debug\\cmTC_0c921.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0c921.dir\\Debug\\\\" /Fd"cmTC_0c921.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          check_isa.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0c921.dir\\Debug\\\\" /Fd"cmTC_0c921.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\_deps\\embree-src\\common\\cmake\\check_isa.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_0c921.exe" /INCREMENTAL /ILK:"cmTC_0c921.dir\\Debug\\cmTC_0c921.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_0c921.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeTmp/Debug/cmTC_0c921.lib" /MACHINE:X64  /machine:x64 /DEPENDENTLOADFLAG:0x2000 /NXCompat /DynamicBase cmTC_0c921.dir\\Debug\\check_isa.obj
          cmTC_0c921.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\Debug\\cmTC_0c921.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_0c921.dir\\Debug\\cmTC_0c921.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_0c921.dir\\Debug\\cmTC_0c921.tlog\\cmTC_0c921.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeTmp\\cmTC_0c921.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.65
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckCompilerFlag.cmake:18 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCXXCompilerFlag.cmake:55 (cmake_check_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:71 (check_cxx_compiler_flag)"
      - "build/_deps/eigen3-src/CMakeLists.txt:88 (ei_add_cxx_compiler_flag)"
    checks:
      - "Performing Test COMPILER_SUPPORT_std=cpp03"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8jva9r"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8jva9r"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "COMPILER_SUPPORT_std=cpp03"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8jva9r'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_6e230.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:57.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\cmTC_6e230.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_6e230.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\Debug\\".
          Creazione directory "cmTC_6e230.dir\\Debug\\cmTC_6e230.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_6e230.dir\\Debug\\cmTC_6e230.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_6e230.dir\\Debug\\\\" /Fd"cmTC_6e230.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          src.cxx
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D COMPILER_SUPPORT_std=cpp03 /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_6e230.dir\\Debug\\\\" /Fd"cmTC_6e230.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\src.cxx"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\Debug\\cmTC_6e230.exe" /INCREMENTAL /ILK:"cmTC_6e230.dir\\Debug\\cmTC_6e230.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8jva9r/Debug/cmTC_6e230.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-8jva9r/Debug/cmTC_6e230.lib" /MACHINE:X64  /machine:x64 cmTC_6e230.dir\\Debug\\src.obj
          cmTC_6e230.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\Debug\\cmTC_6e230.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_6e230.dir\\Debug\\cmTC_6e230.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_6e230.dir\\Debug\\cmTC_6e230.tlog\\cmTC_6e230.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-8jva9r\\cmTC_6e230.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.68
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:108 (try_compile)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:8 (_DetermineVSServicePack_CheckVersionWithTryCompile)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-7bvrug"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-7bvrug"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-7bvrug'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_2e0b8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:58.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7bvrug\\cmTC_2e0b8.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_2e0b8.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7bvrug\\Debug\\".
          Creazione directory "cmTC_2e0b8.dir\\Debug\\cmTC_2e0b8.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_2e0b8.dir\\Debug\\cmTC_2e0b8.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2e0b8.dir\\Debug\\\\" /Fd"cmTC_2e0b8.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_2e0b8.dir\\Debug\\\\" /Fd"cmTC_2e0b8.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7bvrug\\Debug\\cmTC_2e0b8.exe" /INCREMENTAL /ILK:"cmTC_2e0b8.dir\\Debug\\cmTC_2e0b8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-7bvrug/Debug/cmTC_2e0b8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-7bvrug/Debug/cmTC_2e0b8.lib" /MACHINE:X64  /machine:x64 cmTC_2e0b8.dir\\Debug\\return0.obj
          cmTC_2e0b8.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7bvrug\\Debug\\cmTC_2e0b8.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_2e0b8.dir\\Debug\\cmTC_2e0b8.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_2e0b8.dir\\Debug\\cmTC_2e0b8.tlog\\cmTC_2e0b8.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7bvrug\\cmTC_2e0b8.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.71
        
      exitCode: 0
  -
    kind: "try_run-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineVSServicePack.cmake:129 (try_run)"
      - "build/_deps/eigen3-src/cmake/EigenDetermineVSServicePack.cmake:10 (_DetermineVSServicePack_CheckVersionWithTryRun)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:460 (EigenDetermineVSServicePack)"
      - "build/_deps/eigen3-src/cmake/EigenTesting.cmake:598 (ei_get_compilerver)"
      - "build/_deps/eigen3-src/cmake/EigenConfigureTesting.cmake:8 (ei_set_build_string)"
      - "build/_deps/eigen3-src/CMakeLists.txt:487 (include)"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1zxq8k"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1zxq8k"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc -std=c++03 /EHsc /wd4127 /wd4505 /wd4714"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/cmake"
      CMAKE_WARN_DEPRECATED: "FALSE"
    buildResult:
      variable: "_CompileResult"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1zxq8k'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_758ea.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 16:48:59.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1zxq8k\\cmTC_758ea.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_758ea.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1zxq8k\\Debug\\".
          Creazione directory "cmTC_758ea.dir\\Debug\\cmTC_758ea.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_758ea.dir\\Debug\\cmTC_758ea.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_758ea.dir\\Debug\\\\" /Fd"cmTC_758ea.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          return0.cc
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_758ea.dir\\Debug\\\\" /Fd"cmTC_758ea.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /wd4127 /wd4505 /wd4714 /errorReport:queue  -std=c++03 "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\return0.cc"
          cl : warning della riga di comando D9002 : l'opzione sconosciuta '-std=c++03' verrà ignorata
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1zxq8k\\Debug\\cmTC_758ea.exe" /INCREMENTAL /ILK:"cmTC_758ea.dir\\Debug\\cmTC_758ea.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1zxq8k/Debug/cmTC_758ea.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-1zxq8k/Debug/cmTC_758ea.lib" /MACHINE:X64  /machine:x64 cmTC_758ea.dir\\Debug\\return0.obj
          cmTC_758ea.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1zxq8k\\Debug\\cmTC_758ea.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_758ea.dir\\Debug\\cmTC_758ea.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_758ea.dir\\Debug\\cmTC_758ea.tlog\\cmTC_758ea.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1zxq8k\\cmTC_758ea.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:00.72
        
      exitCode: 0
    runResult:
      variable: "_RunResult"
      cached: true
      stdout: |
        19.29.30159.01
      exitCode: 0
...
