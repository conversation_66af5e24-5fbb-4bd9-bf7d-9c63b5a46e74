
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:6 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
      Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
      
      Compilazione avviata 17/06/2025 15:36:26.
      Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" sul nodo 1 (destinazioni predefinite).
      PrepareForBuild:
        Creazione directory "Debug\\".
        Creazione directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creazione di "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc142.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Eliminazione del file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" in corso.
        Aggiornamento timestamp di "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\4.0.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinazioni predefinite) completata.
      
      Compilazione completata.
          Avvisi: 0
          Errori: 0
      
      Tempo trascorso 00:00:01.74
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/4.0.3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-6jcfvz"
      binary: "C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-6jcfvz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-6jcfvz'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/MSBuild/Current/Bin/MSBuild.exe" cmTC_a0c3d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=16.0 /v:n
        Microsoft (R) Build Engine versione 16.11.6+a918ceb31 per .NET Framework
        Copyright (C) Microsoft Corporation. Tutti i diritti sono riservati.
        
        Compilazione avviata 17/06/2025 15:36:29.
        Progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6jcfvz\\cmTC_a0c3d.vcxproj" sul nodo 1 (destinazioni predefinite).
        PrepareForBuild:
          Creazione directory "cmTC_a0c3d.dir\\Debug\\".
          Creazione directory "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6jcfvz\\Debug\\".
          Creazione directory "cmTC_a0c3d.dir\\Debug\\cmTC_a0c3d.tlog\\".
        InitializeBuildStatus:
          Creazione di "cmTC_a0c3d.dir\\Debug\\cmTC_a0c3d.tlog\\unsuccessfulbuild". È stato specificato "AlwaysCreate".
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a0c3d.dir\\Debug\\\\" /Fd"cmTC_a0c3d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler versione 19.29.30159 per x64
          Copyright (C) Microsoft Corporation. Tutti i diritti  sono riservati.
          CMakeCXXCompilerABI.cpp
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_a0c3d.dir\\Debug\\\\" /Fd"cmTC_a0c3d.dir\\Debug\\vc142.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\BuildTools\\VC\\Tools\\MSVC\\14.29.30133\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6jcfvz\\Debug\\cmTC_a0c3d.exe" /INCREMENTAL /ILK:"cmTC_a0c3d.dir\\Debug\\cmTC_a0c3d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-6jcfvz/Debug/cmTC_a0c3d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/CMakeScratch/TryCompile-6jcfvz/Debug/cmTC_a0c3d.lib" /MACHINE:X64  /machine:x64 cmTC_a0c3d.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_a0c3d.vcxproj -> C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6jcfvz\\Debug\\cmTC_a0c3d.exe
        FinalizeBuildStatus:
          Eliminazione del file "cmTC_a0c3d.dir\\Debug\\cmTC_a0c3d.tlog\\unsuccessfulbuild" in corso.
          Aggiornamento timestamp di "cmTC_a0c3d.dir\\Debug\\cmTC_a0c3d.tlog\\cmTC_a0c3d.lastbuildstate".
        Compilazione progetto "C:\\xampp\\htdocs\\progetti\\photon-render\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6jcfvz\\cmTC_a0c3d.vcxproj" (destinazioni predefinite) completata.
        
        Compilazione completata.
            Avvisi: 0
            Errori: 0
        
        Tempo trascorso 00:00:01.20
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:6 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.29.30159.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
