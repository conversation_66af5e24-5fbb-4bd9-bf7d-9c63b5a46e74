# This is the CMakeCache file.
# For build in directory: c:/xampp/htdocs/progetti/photon-render/build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a file.
ADOLC_INCLUDES:PATH=ADOLC_INCLUDES-NOTFOUND

//Path to a library.
ADOLC_LIBRARIES:FILEPATH=ADOLC_LIBRARIES-NOTFOUND

//Path to a library.
BLAS_Accelerate_LIBRARY:FILEPATH=BLAS_Accelerate_LIBRARY-NOTFOUND

//Installation directory of BLAS library
BLAS_DIR:PATH=

//Print some additional information during BLAS libraries detection
BLAS_VERBOSE:BOOL=OFF

//Path to a library.
BLAS_acml_LIBRARY:FILEPATH=BLAS_acml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_acml_mp_LIBRARY:FILEPATH=BLAS_acml_mp_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_LIBRARY:FILEPATH=BLAS_blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_LINUX_LIBRARY:FILEPATH=BLAS_blas_LINUX_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_MAC_LIBRARY:FILEPATH=BLAS_blas_MAC_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_WINDOWS_LIBRARY:FILEPATH=BLAS_blas_WINDOWS_LIBRARY-NOTFOUND

//Path to a library.
BLAS_complib.sgimath_LIBRARY:FILEPATH=BLAS_complib.sgimath_LIBRARY-NOTFOUND

//Path to a library.
BLAS_cxml_LIBRARY:FILEPATH=BLAS_cxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_dxml_LIBRARY:FILEPATH=BLAS_dxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_eigen_blas_LIBRARY:FILEPATH=BLAS_eigen_blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_eigen_blas_static_LIBRARY:FILEPATH=BLAS_eigen_blas_static_LIBRARY-NOTFOUND

//Path to a library.
BLAS_essl_LIBRARY:FILEPATH=BLAS_essl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_esslsmp_LIBRARY:FILEPATH=BLAS_esslsmp_LIBRARY-NOTFOUND

//Path to a library.
BLAS_f77blas_LIBRARY:FILEPATH=BLAS_f77blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_goto2_LIBRARY:FILEPATH=BLAS_goto2_LIBRARY-NOTFOUND

//Path to a file.
BLAS_mkl.h_DIRS:PATH=BLAS_mkl.h_DIRS-NOTFOUND

//Path to a library.
BLAS_mkl_intel_c_dll_LIBRARY:FILEPATH=BLAS_mkl_intel_c_dll_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_intel_lp64_dll_LIBRARY:FILEPATH=BLAS_mkl_intel_lp64_dll_LIBRARY-NOTFOUND

//Path to a library.
BLAS_openblas_LIBRARY:FILEPATH=BLAS_openblas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_refblas_LIBRARY:FILEPATH=BLAS_refblas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_scsl_LIBRARY:FILEPATH=BLAS_scsl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sgemm_LIBRARY:FILEPATH=BLAS_sgemm_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sunperf_LIBRARY:FILEPATH=BLAS_sunperf_LIBRARY-NOTFOUND

//Path to a library.
BLAS_vecLib_LIBRARY:FILEPATH=BLAS_vecLib_LIBRARY-NOTFOUND

//Build benchmarks
BUILD_BENCHMARKS:BOOL=OFF

//Build shared libraries
BUILD_SHARED_LIBS:BOOL=OFF

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Build unit tests
BUILD_TESTS:BOOL=OFF

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=Boost_DIR-NOTFOUND

//Path to a file.
Boost_INCLUDE_DIR:PATH=Boost_INCLUDE_DIR-NOTFOUND

//Path to a file.
CHOLMOD_INCLUDES:PATH=CHOLMOD_INCLUDES-NOTFOUND

//Path to a library.
CHOLMOD_LIBRARIES:FILEPATH=CHOLMOD_LIBRARIES-NOTFOUND

//The directory relative to CMAKE_INSTALL_PREFIX where Eigen3Config.cmake
// is installed
CMAKEPACKAGE_INSTALL_DIR:PATH=share/eigen3/cmake

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/lib.exe

//Build type
CMAKE_BUILD_TYPE:STRING=Release

//List of generated configurations.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/CMakeFiles/pkgRedirects

//Fortran compiler
CMAKE_Fortran_COMPILER:FILEPATH=NOTFOUND

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files/PhotonRender

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//No help, variable specified on the command line.
CMAKE_POLICY_DEFAULT_CMP0002:UNINITIALIZED=OLD

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=PhotonRender

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=0.1.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Test COMPILER_SUPPORT_std=cpp03
COMPILER_SUPPORT_std:UNINITIALIZED=cpp03:INTERNAL=1

//Path to the coverage program that CTest uses for performing coverage
// inspection
COVERAGE_COMMAND:FILEPATH=COVERAGE_COMMAND-NOTFOUND

//Extra command line flags to pass to the coverage tool
COVERAGE_EXTRA_FLAGS:STRING=-l

//Enable to build 7-Zip source packages
CPACK_SOURCE_7Z:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=ON

//How many times to retry timed-out CTest submissions.
CTEST_SUBMIT_RETRY_COUNT:STRING=3

//How long to wait between timed-out CTest submissions.
CTEST_SUBMIT_RETRY_DELAY:STRING=5

//Compile device code in 64 bit mode
CUDA_64_BIT_DEVICE_CODE:BOOL=ON

//Attach the build rule to the CUDA source file.  Enable only when
// the CUDA source file is added to at most one target.
CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE:BOOL=ON

//Generate and parse .cubin files in Device mode.
CUDA_BUILD_CUBIN:BOOL=OFF

//Build in Emulation mode
CUDA_BUILD_EMULATION:BOOL=OFF

//"cudart" library
CUDA_CUDART_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cudart.lib

//"cuda" library (older versions only).
CUDA_CUDA_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cuda.lib

//Directory to put all the output files.  If blank it will default
// to the CMAKE_CURRENT_BINARY_DIR
CUDA_GENERATED_OUTPUT_DIR:PATH=

//Generated file extension
CUDA_HOST_COMPILATION_CPP:BOOL=ON

//Host side compiler used by NVCC
CUDA_HOST_COMPILER:FILEPATH=$(VCInstallDir)Tools/MSVC/$(VCToolsVersion)/bin/Host$(Platform)/$(PlatformTarget)

//Path to a program.
CUDA_NVCC_EXECUTABLE:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/nvcc.exe

//Semi-colon delimit multiple arguments. during all build types.
CUDA_NVCC_FLAGS:STRING=

//Semi-colon delimit multiple arguments. during DEBUG builds.
CUDA_NVCC_FLAGS_DEBUG:STRING=

//Semi-colon delimit multiple arguments. during MINSIZEREL builds.
CUDA_NVCC_FLAGS_MINSIZEREL:STRING=

//Semi-colon delimit multiple arguments. during RELEASE builds.
CUDA_NVCC_FLAGS_RELEASE:STRING=

//Semi-colon delimit multiple arguments. during RELWITHDEBINFO
// builds.
CUDA_NVCC_FLAGS_RELWITHDEBINFO:STRING=

//"OpenCL" library
CUDA_OpenCL_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/OpenCL.lib

//Propagate C/CXX_FLAGS and friends to the host compiler via -Xcompile
CUDA_PROPAGATE_HOST_FLAGS:BOOL=ON

//Path to a file.
CUDA_SDK_ROOT_DIR:PATH=CUDA_SDK_ROOT_DIR-NOTFOUND

//Compile CUDA objects with separable compilation enabled.  Requires
// CUDA 5.0+
CUDA_SEPARABLE_COMPILATION:BOOL=OFF

//Path to a file.
CUDA_TOOLKIT_INCLUDE:PATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/include

//Toolkit location.
CUDA_TOOLKIT_ROOT_DIR:PATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9

//Use the static version of the CUDA runtime library if available
CUDA_USE_STATIC_CUDA_RUNTIME:BOOL=ON

//Print out the commands run while compiling the CUDA source file.
//  With the Makefile generator this defaults to VERBOSE variable
// specified on the command line, but can be forced on with this
// option.
CUDA_VERBOSE_BUILD:BOOL=OFF

//Version of CUDA as computed from nvcc.
CUDA_VERSION:STRING=12.9

//"cublas" library
CUDA_cublas_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cublas.lib

//"cudadevrt" library
CUDA_cudadevrt_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cudadevrt.lib

//static CUDA runtime library
CUDA_cudart_static_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cudart_static.lib

//"cufft" library
CUDA_cufft_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cufft.lib

//"cupti" library
CUDA_cupti_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/extras/CUPTI/lib64/cupti.lib

//"curand" library
CUDA_curand_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/curand.lib

//"cusolver" library
CUDA_cusolver_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cusolver.lib

//"cusparse" library
CUDA_cusparse_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cusparse.lib

//"nppc" library
CUDA_nppc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppc.lib

//"nppial" library
CUDA_nppial_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppial.lib

//"nppicc" library
CUDA_nppicc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppicc.lib

//"nppidei" library
CUDA_nppidei_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppidei.lib

//"nppif" library
CUDA_nppif_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppif.lib

//"nppig" library
CUDA_nppig_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppig.lib

//"nppim" library
CUDA_nppim_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppim.lib

//"nppist" library
CUDA_nppist_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppist.lib

//"nppisu" library
CUDA_nppisu_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppisu.lib

//"nppitc" library
CUDA_nppitc_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/nppitc.lib

//"npps" library
CUDA_npps_LIBRARY:FILEPATH=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/npps.lib

//"nvToolsExt" library
CUDA_nvToolsExt_LIBRARY:FILEPATH=CUDA_nvToolsExt_LIBRARY-NOTFOUND

//"nvcuvenc" library
CUDA_nvcuvenc_LIBRARY:FILEPATH=CUDA_nvcuvenc_LIBRARY-NOTFOUND

//"nvcuvid" library
CUDA_nvcuvid_LIBRARY:FILEPATH=CUDA_nvcuvid_LIBRARY-NOTFOUND

//Maximum time allowed before CTest will kill the test.
DART_TESTING_TIMEOUT:STRING=1500

//Build benchmark suite
EIGEN_BUILD_BTL:BOOL=OFF

//Enable creation of Eigen documentation
EIGEN_BUILD_DOC:BOOL=ON

//No help, variable specified on the command line.
EIGEN_BUILD_TESTING:UNINITIALIZED=OFF

//Regular expression for build error messages to be filtered out
EIGEN_CTEST_ERROR_EXCEPTION:STRING=

//The CUDA compute architecture level to target when compiling
// CUDA code
EIGEN_CUDA_COMPUTE_ARCH:STRING=30

//Target to be built in dashboard mode, default is buildtests
EIGEN_DASHBOARD_BUILD_TARGET:STRING=buildtests

//Enable advanced debugging of assertions
EIGEN_DEBUG_ASSERTS:BOOL=OFF

//Use row-major as default matrix storage order
EIGEN_DEFAULT_TO_ROW_MAJOR:BOOL=OFF

//Use MathJax for rendering math in HTML docs
EIGEN_DOC_USE_MATHJAX:BOOL=ON

//Build internal documentation
EIGEN_INTERNAL_DOCUMENTATION:BOOL=OFF

//Disable checking of assertions using exceptions
EIGEN_NO_ASSERTION_CHECKING:BOOL=OFF

//Split large tests into smaller executables
EIGEN_SPLIT_LARGE_TESTS:BOOL=ON

//Use the triSYCL Sycl implementation (ComputeCPP by default).
EIGEN_SYCL_TRISYCL:BOOL=OFF

//Force generating 32bit code.
EIGEN_TEST_32BIT:BOOL=OFF

//Enable/Disable AVX in tests/examples
EIGEN_TEST_AVX:BOOL=OFF

//Test building the doxygen documentation
EIGEN_TEST_BUILD_DOCUMENTATION:BOOL=OFF

//Options passed to the build command of unit tests
EIGEN_TEST_BUILD_FLAGS:STRING=

//Enable CUDA support in unit tests
EIGEN_TEST_CUDA:BOOL=OFF

//Use clang instead of nvcc to compile the CUDA tests
EIGEN_TEST_CUDA_CLANG:BOOL=OFF

//Additional compiler flags when compiling unit tests.
EIGEN_TEST_CUSTOM_CXX_FLAGS:STRING=

//Additional linker flags when linking unit tests.
EIGEN_TEST_CUSTOM_LINKER_FLAGS:STRING=

//Enable testing with C++11 and C++11 features (e.g. Tensor module).
EIGEN_TEST_CXX11:BOOL=OFF

//Run whole Eigen2 test suite against EIGEN2_SUPPORT
EIGEN_TEST_EIGEN2:BOOL=OFF

//Use external BLAS library for testsuite
EIGEN_TEST_EXTERNAL_BLAS:BOOL=OFF

//Enable/Disable FMA/AVX2 in tests/examples
EIGEN_TEST_FMA:BOOL=OFF

//Add HIP support.
EIGEN_TEST_HIP:BOOL=OFF

//Enable testing of realword sparse matrices contained in the specified
// path
EIGEN_TEST_MATRIX_DIR:STRING=

//Maximal matrix/vector size, default is 320
EIGEN_TEST_MAX_SIZE:STRING=320

//Disable Qt support in unit tests
EIGEN_TEST_NOQT:BOOL=OFF

//Disables C++ exceptions
EIGEN_TEST_NO_EXCEPTIONS:BOOL=OFF

//Disable explicit alignment (hence vectorization) in tests/examples
EIGEN_TEST_NO_EXPLICIT_ALIGNMENT:BOOL=OFF

//Disable explicit vectorization in tests/examples
EIGEN_TEST_NO_EXPLICIT_VECTORIZATION:BOOL=OFF

//Enable OpenGL support in unit tests
EIGEN_TEST_OPENGL:BOOL=OFF

//Enable/Disable OpenMP in tests/examples
EIGEN_TEST_OPENMP:BOOL=OFF

//Enable/Disable SSE2 in tests/examples
EIGEN_TEST_SSE2:BOOL=OFF

//Add Sycl support.
EIGEN_TEST_SYCL:BOOL=OFF

//Force using X87 instructions. Implies no vectorization.
EIGEN_TEST_X87:BOOL=OFF

//C++ namespace to put API symbols into.
EMBREE_API_NAMESPACE:STRING=

//Enables backface culling.
EMBREE_BACKFACE_CULLING:BOOL=OFF

//Enables backface culling for curve primitives.
EMBREE_BACKFACE_CULLING_CURVES:BOOL=OFF

//Enables backface culling for sphere primitives.
EMBREE_BACKFACE_CULLING_SPHERES:BOOL=OFF

//Builds GLFW from source.
EMBREE_BUILD_GLFW_FROM_SOURCE:BOOL=OFF

//Force to download, build, and staticly link Google Benchmark
EMBREE_BUILD_GOOGLE_BENCHMARK_FROM_SOURCE:BOOL=OFF

//Enables double indexed poly layout.
EMBREE_COMPACT_POLYS:BOOL=OFF

//Self intersection avoidance factor for flat curves. Specify floating
// point value in range 0 to inf.
EMBREE_CURVE_SELF_INTERSECTION_AVOIDANCE_FACTOR:STRING=2.0

//Enables self intersection avoidance for ray oriented discs.
EMBREE_DISC_POINT_SELF_INTERSECTION_AVOIDANCE:BOOL=ON

//Enables filter functions.
EMBREE_FILTER_FUNCTION:BOOL=ON

//Enables support for curve geometries.
EMBREE_GEOMETRY_CURVE:BOOL=ON

//Enables support for grid geometries.
EMBREE_GEOMETRY_GRID:BOOL=ON

//Enables support for instances.
EMBREE_GEOMETRY_INSTANCE:BOOL=ON

//Enables support for instance arrays.
EMBREE_GEOMETRY_INSTANCE_ARRAY:BOOL=ON

//Enables support for point geometries.
EMBREE_GEOMETRY_POINT:BOOL=ON

//Enables support for quad geometries.
EMBREE_GEOMETRY_QUAD:BOOL=ON

//Enables support for subdiv geometries.
EMBREE_GEOMETRY_SUBDIVISION:BOOL=ON

//Enables support for triangle geometries.
EMBREE_GEOMETRY_TRIANGLE:BOOL=ON

//Enables support for user geometries.
EMBREE_GEOMETRY_USER:BOOL=ON

//Ignores invalid rays.
EMBREE_IGNORE_INVALID_RAYS:BOOL=OFF

//Install Embree dependencies in binary packages and install
EMBREE_INSTALL_DEPENDENCIES:BOOL=OFF

//Enables AVX ISA.
EMBREE_ISA_AVX:BOOL=ON

//Enables AVX2 ISA.
EMBREE_ISA_AVX2:BOOL=ON

//Enables AVX512 ISA.
EMBREE_ISA_AVX512:BOOL=OFF

//Enables SSE2 ISA.
EMBREE_ISA_SSE2:BOOL=ON

//Enables SSE4.2 ISA.
EMBREE_ISA_SSE42:BOOL=ON

//Build Embree with support for ISPC applications.
EMBREE_ISPC_SUPPORT:BOOL=OFF

//Name of the embree library file (default is embree4)
EMBREE_LIBRARY_NAME:STRING=embree4

//Maximum number of instance levels.
EMBREE_MAX_INSTANCE_LEVEL_COUNT:STRING=1

//Selects highest ISA to support.
EMBREE_MAX_ISA:STRING=NONE

//Enables min-width feature to enlarge curve and point thickness
// to pixel width.
EMBREE_MIN_WIDTH:BOOL=OFF

//Enables ray mask support.
EMBREE_RAY_MASK:BOOL=ON

//Enabled support for ray packets.
EMBREE_RAY_PACKETS:BOOL=ON

//When enabled Embree compiles with stack protection against return
// address overrides.
EMBREE_STACK_PROTECTOR:BOOL=OFF

//Build Embree as a static library.
EMBREE_STATIC_LIB:BOOL=OFF

//Use the static version of the C/C++ runtime library.
EMBREE_STATIC_RUNTIME:BOOL=OFF

//Enables statistic counters.
EMBREE_STAT_COUNTERS:BOOL=OFF

//Enables SYCL GPU support.
EMBREE_SYCL_SUPPORT:BOOL=OFF

//Selects tasking system
EMBREE_TASKING_SYSTEM:STRING=TBB

//The TBB component/library name.
EMBREE_TBB_COMPONENT:STRING=tbb

//Turns benchmarking on.
EMBREE_TESTING_BENCHMARK:BOOL=OFF

//Path to database for benchmarking.
EMBREE_TESTING_BENCHMARK_DATABASE:PATH=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-build

//Intensity of testing (0 = no testing, 1 = verify and tutorials,
// 2 = light testing, 3 = intensive testing, 4 = very intensive
// testing.
EMBREE_TESTING_INTENSITY:STRING=1

//Runs Kocwork as test.
EMBREE_TESTING_KLOCWORK:BOOL=OFF

//Turns on memory checking for some tests.
EMBREE_TESTING_MEMCHECK:BOOL=OFF

//Run only tests with the sycl support.
EMBREE_TESTING_ONLY_SYCL_TESTS:BOOL=OFF

//Packages release as test.
EMBREE_TESTING_PACKAGE:BOOL=OFF

//Uses SDE to run tests for specified CPU.
EMBREE_TESTING_SDE:STRING=OFF

//Enable to build Embree tutorials
EMBREE_TUTORIALS:BOOL=OFF

//Enables GLFW usage in tutorials. When disabled tutorial can only
// render to disk.
EMBREE_TUTORIALS_GLFW:BOOL=ON

//Use google benchmark (note: set benchmark_DIR to benchmark_install_dir/lib/cmake/benchmark)
EMBREE_USE_GOOGLE_BENCHMARK:BOOL=OFF

//Create Embree ZIP package
EMBREE_ZIP_MODE:BOOL=ON

//Value Computed by CMake
Eigen3_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-build

//Value Computed by CMake
Eigen3_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Eigen3_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src

//Value Computed by CMake
EigenBlas_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-build/blas

//Value Computed by CMake
EigenBlas_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
EigenBlas_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/blas

//Value Computed by CMake
EigenDemos_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-build/demos

//Value Computed by CMake
EigenDemos_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
EigenDemos_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/demos

//Value Computed by CMake
EigenDoc_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-build/doc

//Value Computed by CMake
EigenDoc_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
EigenDoc_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/doc

//Value Computed by CMake
EigenLapack_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-build/lapack

//Value Computed by CMake
EigenLapack_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
EigenLapack_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/eigen3-src/lapack

//Directory under which to collect all populated content
FETCHCONTENT_BASE_DIR:PATH=C:/xampp/htdocs/progetti/photon-render/build/_deps

//Disables all attempts to download or update content and assumes
// source dirs already exist
FETCHCONTENT_FULLY_DISCONNECTED:BOOL=OFF

//Enables QUIET option for all content population
FETCHCONTENT_QUIET:BOOL=ON

//When not empty, overrides where to find pre-populated content
// for Eigen3
FETCHCONTENT_SOURCE_DIR_EIGEN3:PATH=

//When not empty, overrides where to find pre-populated content
// for embree
FETCHCONTENT_SOURCE_DIR_EMBREE:PATH=

//When not empty, overrides where to find pre-populated content
// for glfw
FETCHCONTENT_SOURCE_DIR_GLFW:PATH=

//When not empty, overrides where to find pre-populated content
// for stb
FETCHCONTENT_SOURCE_DIR_STB:PATH=

//When not empty, overrides where to find pre-populated content
// for TBB
FETCHCONTENT_SOURCE_DIR_TBB:PATH=

//Enables UPDATE_DISCONNECTED behavior for all content population
FETCHCONTENT_UPDATES_DISCONNECTED:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of Eigen3
FETCHCONTENT_UPDATES_DISCONNECTED_EIGEN3:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of embree
FETCHCONTENT_UPDATES_DISCONNECTED_EMBREE:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of glfw
FETCHCONTENT_UPDATES_DISCONNECTED_GLFW:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of stb
FETCHCONTENT_UPDATES_DISCONNECTED_STB:BOOL=OFF

//Enables UPDATE_DISCONNECTED behavior just for population of TBB
FETCHCONTENT_UPDATES_DISCONNECTED_TBB:BOOL=OFF

//Path to a program.
GITCOMMAND:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Value Computed by CMake
GLFW_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/glfw-build

//Generate installation target
GLFW_INSTALL:BOOL=ON

//Value Computed by CMake
GLFW_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
GLFW_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/glfw-src

//Force use of high-performance GPU on hybrid systems
GLFW_USE_HYBRID_HPG:BOOL=OFF

//Assume the Vulkan loader is linked with the application
GLFW_VULKAN_STATIC:BOOL=OFF

//Path to a file.
GOOGLEHASH_INCLUDES:PATH=GOOGLEHASH_INCLUDES-NOTFOUND

//Installation directory of HWLOC library
HWLOC_DIR:PATH=

//The directory relative to CMAKE_INSTALL_PREFIX where Eigen header
// files are installed
INCLUDE_INSTALL_DIR:PATH=include/eigen3

//Path to a file.
KLU_INCLUDES:PATH=KLU_INCLUDES-NOTFOUND

//Path to a library.
KLU_LIBRARIES:FILEPATH=KLU_LIBRARIES-NOTFOUND

//Command to build the project
MAKECOMMAND:STRING="C:\Program Files\CMake\bin\cmake.exe" --build . --config "${CTEST_CONFIGURATION_TYPE}"

//Path to the memory checking command, used for memory error detection.
MEMORYCHECK_COMMAND:FILEPATH=MEMORYCHECK_COMMAND-NOTFOUND

//File that contains suppressions for the memory checker
MEMORYCHECK_SUPPRESSIONS_FILE:FILEPATH=

//Path to a file.
MPFR_INCLUDES:PATH=MPFR_INCLUDES-NOTFOUND

//Path to a library.
MPFR_LIBRARIES:FILEPATH=MPFR_LIBRARIES-NOTFOUND

//Path to a file.
MSVC_REDIST_DIR:PATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/VC/Redist/MSVC/14.29.30133

//Path to a library.
OMP_gomp_LIBRARY:FILEPATH=OMP_gomp_LIBRARY-NOTFOUND

//Path to a library.
OMP_iomp5_LIBRARY:FILEPATH=OMP_iomp5_LIBRARY-NOTFOUND

//OpenGL library for win32
OPENGL_gl_LIBRARY:STRING=opengl32

//GLU library for win32
OPENGL_glu_LIBRARY:STRING=glu32

//CXX compiler flags for OpenMP parallelization
OpenMP_CXX_FLAGS:STRING=-openmp

//CXX compiler libraries for OpenMP parallelization
OpenMP_CXX_LIB_NAMES:STRING=

//C compiler flags for OpenMP parallelization
OpenMP_C_FLAGS:STRING=-openmp

//C compiler libraries for OpenMP parallelization
OpenMP_C_LIB_NAMES:STRING=

//Installation directory of PASTIX library
PASTIX_DIR:PATH=

//Path to a library.
PASTIX_M_m_LIBRARY:FILEPATH=PASTIX_M_m_LIBRARY-NOTFOUND

//Path to a file.
PASTIX_pastix_nompi.h_INCLUDE_DIRS:PATH=PASTIX_pastix_nompi.h_INCLUDE_DIRS-NOTFOUND

//The directory relative to CMAKE_INSTALL_PREFIX where eigen3.pc
// is installed
PKGCONFIG_INSTALL_DIR:PATH=share/pkgconfig

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=PKG_CONFIG_EXECUTABLE-NOTFOUND

//Value Computed by CMake
PhotonRender_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build

//Value Computed by CMake
PhotonRender_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
PhotonRender_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render

QT_QMAKE_EXECUTABLE:FILEPATH=NOTFOUND

//Path to a program.
SDE_EXE:FILEPATH=SDE_EXE-NOTFOUND

//Name of the computer/site where compile is being run
SITE:STRING=OMEN

//Path to a file.
SPQR_INCLUDES:PATH=SPQR_INCLUDES-NOTFOUND

//Path to a library.
SPQR_LIBRARIES:FILEPATH=SPQR_LIBRARIES-NOTFOUND

//Path to a file.
SUPERLU_INCLUDES:PATH=SUPERLU_INCLUDES-NOTFOUND

//Path to a library.
SUPERLU_LIBRARIES:FILEPATH=SUPERLU_LIBRARIES-NOTFOUND

//Enable tbb4py build
TBB4PY_BUILD:BOOL=OFF

//Enable tbbmalloc build
TBBMALLOC_BUILD:BOOL=ON

//Enable tbbmalloc_proxy build
TBBMALLOC_PROXY_BUILD:BOOL=ON

//Value Computed by CMake
TBB_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-build

//Enable tbb build
TBB_BUILD:BOOL=ON

//Enable preview features of the library
TBB_CPF:BOOL=OFF

//Disable HWLOC automatic search by pkg-config tool
TBB_DISABLE_HWLOC_AUTOMATIC_SEARCH:BOOL=OFF

//Enable Interprocedural Optimization (IPO) during the compilation
TBB_ENABLE_IPO:BOOL=ON

//Enable examples
TBB_EXAMPLES:BOOL=OFF

//Enable search for external oneTBB using find_package instead
// of build from sources
TBB_FIND_PACKAGE:BOOL=OFF

//Enable fuzz testing
TBB_FUZZ_TESTING:BOOL=OFF

//Enable installation
TBB_INSTALL:BOOL=ON

//Enable auto-generated vars installation
TBB_INSTALL_VARS:BOOL=OFF

//Value Computed by CMake
TBB_IS_TOP_LEVEL:STATIC=OFF

//Apply /APPCONTAINER:NO (for testing binaries for Windows Store)
TBB_NO_APPCONTAINER:BOOL=OFF

//Sanitizer parameter passed to compiler/linker
TBB_SANITIZE:STRING=

//Value Computed by CMake
TBB_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/tbb-src

//Treat compiler warnings as errors
TBB_STRICT:BOOL=ON

//Enable testing
TBB_TEST:BOOL=ON

//Generate test specification (Doxygen)
TBB_TEST_SPEC:BOOL=OFF

//Enable scan for memory leaks using Valgrind
TBB_VALGRIND_MEMCHECK:BOOL=OFF

//Build as Universal Windows Driver (UWD)
TBB_WINDOWS_DRIVER:BOOL=OFF

//Path to a file.
UMFPACK_INCLUDES:PATH=UMFPACK_INCLUDES-NOTFOUND

//Path to a library.
UMFPACK_LIBRARIES:FILEPATH=UMFPACK_LIBRARIES-NOTFOUND

//Enable CUDA support
USE_CUDA:BOOL=OFF

//Use MSVC runtime library DLL
USE_MSVC_RUNTIME_LIBRARY_DLL:BOOL=ON

//Enable OpenMP
USE_OPENMP:BOOL=ON

//Enable OptiX support
USE_OPTIX:BOOL=OFF

//Dependencies for the target
_test_malloc_atexit_LIB_DEPENDS:STATIC=general;TBB::tbbmalloc_proxy;general;TBB::tbbmalloc;

//Dependencies for the target
_test_malloc_used_by_lib_LIB_DEPENDS:STATIC=general;TBB::tbbmalloc;

//Dependencies for the target
eigen_lapack_LIB_DEPENDS:STATIC=general;eigen_blas;

//Value Computed by CMake
embree4_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-build

//Value Computed by CMake
embree4_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
embree4_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src

//Dependencies for the target
embree_LIB_DEPENDS:STATIC=general;embree_sse42;general;embree_avx;general;embree_avx2;general;sys;general;math;general;simd;general;lexers;general;tasking;

//Dependencies for the target
embree_avx2_LIB_DEPENDS:STATIC=general;tasking;

//Dependencies for the target
embree_avx_LIB_DEPENDS:STATIC=general;tasking;

//Value Computed by CMake
embree_info_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-build/tutorials/embree_info

//Value Computed by CMake
embree_info_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
embree_info_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/tutorials/embree_info

//Dependencies for the target
embree_sse42_LIB_DEPENDS:STATIC=general;tasking;

//The directory containing a CMake configuration file for glfw3.
glfw3_DIR:PATH=glfw3_DIR-NOTFOUND

//Dependencies for the target
image_LIB_DEPENDS:STATIC=general;sys;general;math;

//Dependencies for the target
lexers_LIB_DEPENDS:STATIC=general;sys;general;math;

//Value Computed by CMake
minimal_BINARY_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-build/tutorials/minimal

//Value Computed by CMake
minimal_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
minimal_SOURCE_DIR:STATIC=C:/xampp/htdocs/progetti/photon-render/build/_deps/embree-src/tutorials/minimal

//Dependencies for the target
tasking_LIB_DEPENDS:STATIC=general;TBB::tbb;

//Dependencies for the target
tbbmalloc_proxy_LIB_DEPENDS:STATIC=general;TBB::tbbmalloc;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: ADOLC_INCLUDES
ADOLC_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: ADOLC_LIBRARIES
ADOLC_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_Accelerate_LIBRARY
BLAS_Accelerate_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_DIR
BLAS_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_VERBOSE
BLAS_VERBOSE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_acml_LIBRARY
BLAS_acml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_acml_mp_LIBRARY
BLAS_acml_mp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_LIBRARY
BLAS_blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_LINUX_LIBRARY
BLAS_blas_LINUX_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_MAC_LIBRARY
BLAS_blas_MAC_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_WINDOWS_LIBRARY
BLAS_blas_WINDOWS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_complib.sgimath_LIBRARY
BLAS_complib.sgimath_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_cxml_LIBRARY
BLAS_cxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_dxml_LIBRARY
BLAS_dxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_eigen_blas_LIBRARY
BLAS_eigen_blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_eigen_blas_static_LIBRARY
BLAS_eigen_blas_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_essl_LIBRARY
BLAS_essl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_esslsmp_LIBRARY
BLAS_esslsmp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_f77blas_LIBRARY
BLAS_f77blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_goto2_LIBRARY
BLAS_goto2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl.h_DIRS
BLAS_mkl.h_DIRS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_c_dll_LIBRARY
BLAS_mkl_intel_c_dll_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_lp64_dll_LIBRARY
BLAS_mkl_intel_lp64_dll_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_openblas_LIBRARY
BLAS_openblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_refblas_LIBRARY
BLAS_refblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_scsl_LIBRARY
BLAS_scsl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sgemm_LIBRARY
BLAS_sgemm_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sunperf_LIBRARY
BLAS_sunperf_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_vecLib_LIBRARY
BLAS_vecLib_LIBRARY-ADVANCED:INTERNAL=1
//build documentation (internal only)
BUILD_DOC:INTERNAL=OFF
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CHOLMOD_INCLUDES
CHOLMOD_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CHOLMOD_LIBRARIES
CHOLMOD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/xampp/htdocs/progetti/photon-render/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//ADVANCED property for variable: CMAKE_CTEST_COMMAND
CMAKE_CTEST_COMMAND-ADVANCED:INTERNAL=1
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Whether to issue deprecation errors for macros and functions.
CMAKE_ERROR_DEPRECATED:INTERNAL=FALSE
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//ADVANCED property for variable: CMAKE_Fortran_COMPILER
CMAKE_Fortran_COMPILER-ADVANCED:INTERNAL=1
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 16 2019
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=x64
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/xampp/htdocs/progetti/photon-render
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=34
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-4.0
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Suppress errors that are meant for the author of the CMakeLists.txt
// files.
CMAKE_SUPPRESS_DEVELOPER_ERRORS:INTERNAL=TRUE
//Suppress Warnings that are meant for the author of the CMakeLists.txt
// files.
CMAKE_SUPPRESS_DEVELOPER_WARNINGS:INTERNAL=TRUE
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Whether to issue warnings for deprecated functionality.
CMAKE_WARN_DEPRECATED:INTERNAL=FALSE
//Test COMPILER_HAS_SYCL_SUPPORT
COMPILER_HAS_SYCL_SUPPORT:INTERNAL=1
//Result of TRY_COMPILE
COMPILER_SUPPORTS_AVX:INTERNAL=TRUE
//Result of TRY_COMPILE
COMPILER_SUPPORTS_AVX2:INTERNAL=TRUE
//Result of TRY_COMPILE
COMPILER_SUPPORTS_AVX512:INTERNAL=TRUE
//Test COMPILER_SUPPORT_FASTMATH
COMPILER_SUPPORT_FASTMATH:INTERNAL=1
//Test COMPILER_SUPPORT_OPENMP
COMPILER_SUPPORT_OPENMP:INTERNAL=1
//Test COMPILER_SUPPORT_std=cpp03
COMPILER_SUPPORT_std=cpp03:INTERNAL=1
//ADVANCED property for variable: COVERAGE_COMMAND
COVERAGE_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: COVERAGE_EXTRA_FLAGS
COVERAGE_EXTRA_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_7Z
CPACK_SOURCE_7Z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_COUNT
CTEST_SUBMIT_RETRY_COUNT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CTEST_SUBMIT_RETRY_DELAY
CTEST_SUBMIT_RETRY_DELAY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_64_BIT_DEVICE_CODE
CUDA_64_BIT_DEVICE_CODE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE
CUDA_ATTACH_VS_BUILD_RULE_TO_CUDA_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_BUILD_CUBIN
CUDA_BUILD_CUBIN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_BUILD_EMULATION
CUDA_BUILD_EMULATION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDART_LIBRARY
CUDA_CUDART_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_CUDA_LIBRARY
CUDA_CUDA_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_GENERATED_OUTPUT_DIR
CUDA_GENERATED_OUTPUT_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_HOST_COMPILATION_CPP
CUDA_HOST_COMPILATION_CPP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_EXECUTABLE
CUDA_NVCC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS
CUDA_NVCC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_DEBUG
CUDA_NVCC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_MINSIZEREL
CUDA_NVCC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_RELEASE
CUDA_NVCC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_NVCC_FLAGS_RELWITHDEBINFO
CUDA_NVCC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_OpenCL_LIBRARY
CUDA_OpenCL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_PROPAGATE_HOST_FLAGS
CUDA_PROPAGATE_HOST_FLAGS-ADVANCED:INTERNAL=1
//This is the value of the last time CUDA_SDK_ROOT_DIR was set
// successfully.
CUDA_SDK_ROOT_DIR_INTERNAL:INTERNAL=CUDA_SDK_ROOT_DIR-NOTFOUND
//ADVANCED property for variable: CUDA_SEPARABLE_COMPILATION
CUDA_SEPARABLE_COMPILATION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_TOOLKIT_INCLUDE
CUDA_TOOLKIT_INCLUDE-ADVANCED:INTERNAL=1
//This is the value of the last time CUDA_TOOLKIT_ROOT_DIR was
// set successfully.
CUDA_TOOLKIT_ROOT_DIR_INTERNAL:INTERNAL=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9
//This is the value of the last time CUDA_TOOLKIT_TARGET_DIR was
// set successfully.
CUDA_TOOLKIT_TARGET_DIR_INTERNAL:INTERNAL=C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9
//ADVANCED property for variable: CUDA_VERBOSE_BUILD
CUDA_VERBOSE_BUILD-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_VERSION
CUDA_VERSION-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cublas_LIBRARY
CUDA_cublas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudadevrt_LIBRARY
CUDA_cudadevrt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cudart_static_LIBRARY
CUDA_cudart_static_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cufft_LIBRARY
CUDA_cufft_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cupti_LIBRARY
CUDA_cupti_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_curand_LIBRARY
CUDA_curand_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusolver_LIBRARY
CUDA_cusolver_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_cusparse_LIBRARY
CUDA_cusparse_LIBRARY-ADVANCED:INTERNAL=1
//Location of make2cmake.cmake
CUDA_make2cmake:INTERNAL=C:/Program Files/CMake/share/cmake-4.0/Modules/FindCUDA/make2cmake.cmake
//ADVANCED property for variable: CUDA_nppc_LIBRARY
CUDA_nppc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppial_LIBRARY
CUDA_nppial_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppicc_LIBRARY
CUDA_nppicc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppidei_LIBRARY
CUDA_nppidei_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppif_LIBRARY
CUDA_nppif_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppig_LIBRARY
CUDA_nppig_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppim_LIBRARY
CUDA_nppim_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppist_LIBRARY
CUDA_nppist_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppisu_LIBRARY
CUDA_nppisu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nppitc_LIBRARY
CUDA_nppitc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_npps_LIBRARY
CUDA_npps_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvToolsExt_LIBRARY
CUDA_nvToolsExt_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvcuvenc_LIBRARY
CUDA_nvcuvenc_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CUDA_nvcuvid_LIBRARY
CUDA_nvcuvid_LIBRARY-ADVANCED:INTERNAL=1
//Location of parse_cubin.cmake
CUDA_parse_cubin:INTERNAL=C:/Program Files/CMake/share/cmake-4.0/Modules/FindCUDA/parse_cubin.cmake
//Location of run_nvcc.cmake
CUDA_run_nvcc:INTERNAL=C:/Program Files/CMake/share/cmake-4.0/Modules/FindCUDA/run_nvcc.cmake
//ADVANCED property for variable: DART_TESTING_TIMEOUT
DART_TESTING_TIMEOUT-ADVANCED:INTERNAL=1
//Default CMake configuration types set.
DEFAULT_CMAKE_CONFIGURATION_TYPES_SET:INTERNAL=ON
//Test EIGEN_COMPILER_SUPPORT_CPP11
EIGEN_COMPILER_SUPPORT_CPP11:INTERNAL=1
//ADVANCED property for variable: EIGEN_TEST_EIGEN2
EIGEN_TEST_EIGEN2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: EMBREE_INSTALL_DEPENDENCIES
EMBREE_INSTALL_DEPENDENCIES-ADVANCED:INTERNAL=1
//STRINGS property for variable: EMBREE_MAX_ISA
EMBREE_MAX_ISA-STRINGS:INTERNAL=NONE;SSE2;SSE4.2;AVX;AVX2;AVX512;DEFAULT
//Applys an optional patch to GLFW.
EMBREE_PATCH_GLFW_SOURCE:INTERNAL=OFF
//ADVANCED property for variable: EMBREE_STATIC_LIB
EMBREE_STATIC_LIB-ADVANCED:INTERNAL=0
//STRINGS property for variable: EMBREE_TASKING_SYSTEM
EMBREE_TASKING_SYSTEM-STRINGS:INTERNAL=TBB;INTERNAL;PPL
//STRINGS property for variable: EMBREE_TESTING_INTENSITY
EMBREE_TESTING_INTENSITY-STRINGS:INTERNAL=0;1;2;3;4
//STRINGS property for variable: EMBREE_TESTING_SDE
EMBREE_TESTING_SDE-STRINGS:INTERNAL=OFF;pnr;nhm;wsm;snb;ivb;hsw;bdw;knl;skl;skx;cnl
EMBREE_TEST_bvh_access_DEFINED:INTERNAL=1
EMBREE_TEST_bvh_access_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_bvh_access_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_bvh_builder_DEFINED:INTERNAL=1
EMBREE_TEST_bvh_builder_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_bvh_builder_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_closest_point_DEFINED:INTERNAL=1
EMBREE_TEST_closest_point_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_closest_point_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_collide_DEFINED:INTERNAL=1
EMBREE_TEST_collide_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_collide_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_curve_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_curve_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_curve_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_displacement_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_displacement_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_displacement_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_dynamic_scene_DEFINED:INTERNAL=1
EMBREE_TEST_dynamic_scene_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_dynamic_scene_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_embree_info_DEFINED:INTERNAL=1
EMBREE_TEST_embree_tests_DEFINED:INTERNAL=1
EMBREE_TEST_embree_tests_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_embree_tests_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_DEFINED:INTERNAL=1
EMBREE_TEST_embree_verify_benchmark_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_benchmark_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_benchmark_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_i2_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_i2_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_i2_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_memcheck_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_memcheck_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_memcheck_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_embree_verify_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_forest_DEFINED:INTERNAL=1
EMBREE_TEST_forest_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_forest_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_grid_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_grid_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_grid_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_hair_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_hair_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_hair_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_instanced_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_instanced_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_instanced_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_interpolation_DEFINED:INTERNAL=1
EMBREE_TEST_interpolation_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_interpolation_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_intersection_filter_DEFINED:INTERNAL=1
EMBREE_TEST_intersection_filter_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_intersection_filter_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_lazy_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_lazy_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_lazy_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_minimal_DEFINED:INTERNAL=1
EMBREE_TEST_motion_blur_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_motion_blur_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_motion_blur_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_multi_instanced_geometry_DEFINED:INTERNAL=0
EMBREE_TEST_multi_instanced_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_multi_instanced_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_multiscene_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_multiscene_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_multiscene_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_grids_robust_cornell_box_1_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_grids_robust_cornell_box_1_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_grids_robust_cornell_box_1_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_quads_robust_cornell_box_1_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_quads_robust_cornell_box_1_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_quads_robust_cornell_box_1_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_1_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_1_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_1_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_2_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_2_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_2_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_3_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_3_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_3_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_4_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_4_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_next_hit_triangle_robust_cornell_box_4_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_pathtracer_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_point_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_point_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_point_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_flat_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_flat_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_normal_oriented_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_normal_oriented_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bezier_round_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bezier_round_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_flat_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_flat_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_normal_oriented_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_normal_oriented_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_bspline_round_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_bspline_round_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_flat_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_flat_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_normal_oriented_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_catmull_rom_round_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_catmull_rom_round_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_flat_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_flat_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_normal_oriented_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_normal_oriented_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_round_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_round_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_round_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_round_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_hermite_round_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_hermite_round_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_cone_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_cone_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_flat_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_flat_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_curves_linear_round_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_curves_linear_round_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_deform_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_deform_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_deform_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_deform_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_deform_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_deform_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_grid_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_grid_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_disc_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_disc_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_Ng_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_eyelight_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_occlusion_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_primID_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_uv_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_disc_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_disc_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_disc_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_disc_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_oriented_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_oriented_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_mblur_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_mblur_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_mblur_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_mblur_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_mblur_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_mblur_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_points_sphere_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_points_sphere_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_Ng_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_Ng_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_eyelight_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_Ng_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_eyelight_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_occlusion_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_primID_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_uv_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_occlusion_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_primID_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_uv_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_deform_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_eyelight_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_Ng_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_eyelight_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_occlusion_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_primID_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_uv_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_occlusion_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_primID_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_uv_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_quad_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_deform_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_deform_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_deform_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_deform_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_deform_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_deform_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_subdiv_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_subdiv_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_deform_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_deform_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_degenerate_deform_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_degenerate_deform_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_degenerate_deform_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_degenerate_deform_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_degenerate_deform_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_degenerate_deform_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_Ng_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_far_Ng_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_Ng_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_eyelight_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_far_eyelight_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_eyelight_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_far_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_far_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_far_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_far_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_occlusion_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_occlusion_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_occlusion_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_primID_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_primID_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_primID_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_uv_DEFINED:INTERNAL=1
EMBREE_TEST_prim_triangle_uv_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_prim_triangle_uv_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_quaternion_motion_blur_DEFINED:INTERNAL=1
EMBREE_TEST_quaternion_motion_blur_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_quaternion_motion_blur_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_ray_mask_DEFINED:INTERNAL=0
EMBREE_TEST_ray_mask_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_ray_mask_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_subdivision_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_subdivision_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_subdivision_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_triangle_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_triangle_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_triangle_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_user_geometry_DEFINED:INTERNAL=1
EMBREE_TEST_user_geometry_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_user_geometry_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_curve5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_hair1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_flat_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_oriented_hermite_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_oriented_hermite_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_oriented_hermite_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_min_width_round_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_curves_round_line_segments_9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bezier_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_bspline_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_catmulrom_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_hermite_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_furball_linear_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_grids.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_grids.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_grids.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_quads.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_quads.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_quads.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_triangles.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_triangles.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_furball_spheres_triangles.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_linear_instance_linear_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_linear_instance_linear_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_linear_instance_linear_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_oriented_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_oriented_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_oriented_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_spheres.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_spheres.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_min_width_spheres.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_cylinder.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_cylinder.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_cylinder.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_no_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_no_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_no_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_all.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_all.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_all.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_corners.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_corners.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_pin_corners.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_smooth_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_smooth_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_coherent_models_subdiv_subdiv_smooth_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_curve5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_hair1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_flat_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_oriented_hermite_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_oriented_hermite_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_oriented_hermite_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_min_width_round_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_curves_round_line_segments_9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bezier_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_bspline_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_catmulrom_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_hermite_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_furball_linear_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_grids.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_grids.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_grids.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_quads.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_quads.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_quads.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_triangles.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_triangles.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_furball_spheres_triangles.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_linear_instance_linear_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_linear_instance_linear_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_linear_instance_linear_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_oriented_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_oriented_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_oriented_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_spheres.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_spheres.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_min_width_spheres.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_cylinder.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_cylinder.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_cylinder.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_no_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_no_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_no_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_all.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_all.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_all.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_corners.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_corners.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_pin_corners.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_smooth_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_smooth_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_grid_coherent_models_subdiv_subdiv_smooth_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_curve5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_hair1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_flat_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_oriented_hermite_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_oriented_hermite_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_oriented_hermite_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_min_width_round_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_curves_round_line_segments_9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bezier_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_bspline_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_catmulrom_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_hermite_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_furball_linear_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_grids.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_grids.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_grids.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_quads.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_quads.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_quads.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_triangles.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_triangles.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_furball_spheres_triangles.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_linear_instance_linear_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_linear_instance_linear_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_linear_instance_linear_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_oriented_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_oriented_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_oriented_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_spheres.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_spheres.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_min_width_spheres.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_cylinder.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_cylinder.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_cylinder.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_no_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_no_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_no_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_all.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_all.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_all.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_corners.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_corners.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_pin_corners.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_smooth_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_smooth_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_models_subdiv_subdiv_smooth_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_curve5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_hair1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_flat_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_oriented_hermite_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_oriented_hermite_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_oriented_hermite_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_bezier_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_bezier_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_bezier_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_linear_curves.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_linear_curves.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_min_width_round_linear_curves.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_bspline_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_bspline_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_bspline_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_curve4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_hermite_curve_twisted.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_hermite_curve_twisted.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_oriented_hermite_curve_twisted.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_curves_round_line_segments_9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bezier_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_bspline_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_catmulrom_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_normaloriented.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_normaloriented.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_hermite_normaloriented.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_flat.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_flat.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_flat.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_round.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_round.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_furball_linear_round.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_grids.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_grids.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_grids.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_quads.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_quads.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_quads.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_triangles.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_triangles.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_furball_spheres_triangles.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_curves_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_linear_instance_linear_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_linear_instance_linear_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_linear_instance_linear_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur2.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur2.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_lines_msmblur2.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_curve.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_curve.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_curve.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_grid.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_grid.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_grid.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_instancing.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_instancing.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_instancing.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_line.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_line.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_line.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_oriented_disc.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_sphere.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_sphere.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_sphere.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_triangle.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_triangle.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_msmblur_mblur_time_range_triangle.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_oriented_discs.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_oriented_discs.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_oriented_discs.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_spheres.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_spheres.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_min_width_spheres.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_points.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_points.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_points_points.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_quaternion_motion_blur_quaternion_quad.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_cylinder.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_cylinder.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_cylinder.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv0.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv0.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv0.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv1.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv1.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv1.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv3.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv3.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv3.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv4.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv4.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv4.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv5.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv5.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv5.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv6.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv6.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv6.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv7.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv7.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv7.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv8.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv8.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv8.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv9.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv9.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv9.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_no_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_no_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_no_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_all.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_all.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_all.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_corners.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_corners.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_pin_corners.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_smooth_boundary.ecs_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_smooth_boundary.ecs_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_viewer_quad_coherent_models_subdiv_subdiv_smooth_boundary.ecs_sycl_DEFINED:INTERNAL=0
EMBREE_TEST_voronoi_DEFINED:INTERNAL=1
EMBREE_TEST_voronoi_ispc_DEFINED:INTERNAL=0
EMBREE_TEST_voronoi_sycl_DEFINED:INTERNAL=0
//ADVANCED property for variable: EMBREE_ZIP_MODE
EMBREE_ZIP_MODE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_BASE_DIR
FETCHCONTENT_BASE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_FULLY_DISCONNECTED
FETCHCONTENT_FULLY_DISCONNECTED-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_QUIET
FETCHCONTENT_QUIET-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_SOURCE_DIR_GLFW
FETCHCONTENT_SOURCE_DIR_GLFW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_UPDATES_DISCONNECTED
FETCHCONTENT_UPDATES_DISCONNECTED-ADVANCED:INTERNAL=1
//ADVANCED property for variable: FETCHCONTENT_UPDATES_DISCONNECTED_GLFW
FETCHCONTENT_UPDATES_DISCONNECTED_GLFW-ADVANCED:INTERNAL=1
//Details about finding CUDA
FIND_PACKAGE_MESSAGE_DETAILS_CUDA:INTERNAL=[C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/bin/nvcc.exe][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/include][C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v12.9/lib/x64/cudart_static.lib][v12.9(7.0)]
//Details about finding Git
FIND_PACKAGE_MESSAGE_DETAILS_Git:INTERNAL=[C:/Program Files/Git/cmd/git.exe][v2.50.0.windows.1()]
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[opengl32][ ][v()]
//Details about finding OpenMP
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP:INTERNAL=[TRUE][TRUE][ ][v2.0()]
//Details about finding OpenMP_C
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_C:INTERNAL=[-openmp][v2.0()]
//Details about finding OpenMP_CXX
FIND_PACKAGE_MESSAGE_DETAILS_OpenMP_CXX:INTERNAL=[-openmp][v2.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GITCOMMAND
GITCOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Build the GLFW documentation
GLFW_BUILD_DOCS:INTERNAL=OFF
//Build the GLFW example programs
GLFW_BUILD_EXAMPLES:INTERNAL=OFF
//Build the GLFW test programs
GLFW_BUILD_TESTS:INTERNAL=OFF
//ADVANCED property for variable: GOOGLEHASH_INCLUDES
GOOGLEHASH_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: KLU_INCLUDES
KLU_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: KLU_LIBRARIES
KLU_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MAKECOMMAND
MAKECOMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_COMMAND
MEMORYCHECK_COMMAND-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MEMORYCHECK_SUPPRESSIONS_FILE
MEMORYCHECK_SUPPRESSIONS_FILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPFR_INCLUDES
MPFR_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MPFR_LIBRARIES
MPFR_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MSVC_REDIST_DIR
MSVC_REDIST_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OMP_gomp_LIBRARY
OMP_gomp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OMP_iomp5_LIBRARY
OMP_iomp5_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_CXX_openmp:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_COMPILE_RESULT_C_openmp:INTERNAL=TRUE
//ADVANCED property for variable: OpenMP_CXX_FLAGS
OpenMP_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_CXX_LIB_NAMES
OpenMP_CXX_LIB_NAMES-ADVANCED:INTERNAL=1
//CXX compiler's OpenMP specification date
OpenMP_CXX_SPEC_DATE:INTERNAL=200203
//ADVANCED property for variable: OpenMP_C_FLAGS
OpenMP_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenMP_C_LIB_NAMES
OpenMP_C_LIB_NAMES-ADVANCED:INTERNAL=1
//C compiler's OpenMP specification date
OpenMP_C_SPEC_DATE:INTERNAL=200203
//Result of TRY_COMPILE
OpenMP_SPECTEST_CXX_:INTERNAL=TRUE
//Result of TRY_COMPILE
OpenMP_SPECTEST_C_:INTERNAL=TRUE
//ADVANCED property for variable: PASTIX_M_m_LIBRARY
PASTIX_M_m_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SITE
SITE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPQR_INCLUDES
SPQR_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SPQR_LIBRARIES
SPQR_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SUPERLU_INCLUDES
SUPERLU_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: SUPERLU_LIBRARIES
SUPERLU_LIBRARIES-ADVANCED:INTERNAL=1
//STRINGS property for variable: TBB_SANITIZE
TBB_SANITIZE-STRINGS:INTERNAL=thread;memory;leak;address -fno-omit-frame-pointer
//ADVANCED property for variable: UMFPACK_INCLUDES
UMFPACK_INCLUDES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: UMFPACK_LIBRARIES
UMFPACK_LIBRARIES-ADVANCED:INTERNAL=1
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=Boost_INCLUDE_DIR-NOTFOUND
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=TRUE
//Result of TRY_COMPILE
_CompileResult:INTERNAL=TRUE
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files/PhotonRender
//Result of try_run()
_RunResult:INTERNAL=0
//The Visual Studio Release with Service Pack
my_service_pack:INTERNAL=19.29.30159.0
//Test standard_math_library_linked_to_automatically
standard_math_library_linked_to_automatically:INTERNAL=1

