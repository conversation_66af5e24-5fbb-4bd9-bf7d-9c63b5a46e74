// src/core/scene/camera.cpp
// PhotonRender - Professional Rendering Engine for SketchUp
// Camera system implementation

#include "camera.hpp"
#include "../sampler/sampler.hpp"
#include <cmath>
#include <algorithm>

namespace photon {

// Base Camera implementation
RayDifferential Camera::generateRayDifferential(float u, float v, Sampler& sampler) const {
    // Default implementation - generate main ray and approximate differentials
    RayDifferential rd(generateRay(u, v, sampler));
    
    // Small offset for differential estimation
    const float eps = 0.001f;
    
    // Generate rays with small offsets
    Ray rxRay = generateRay(u + eps, v, sampler);
    Ray ryRay = generateRay(u, v + eps, sampler);
    
    rd.hasDifferentials = true;
    rd.rxOrigin = rxRay.o;
    rd.ryOrigin = ryRay.o;
    rd.rxDirection = rxRay.d;
    rd.ryDirection = ryRay.d;
    
    return rd;
}

// PerspectiveCamera implementation
PerspectiveCamera::PerspectiveCamera(const Point3& position, const Point3& target, const Vec3& up,
                                   float fov, float aspect, float nearPlane, float farPlane)
    : m_position(position), m_target(target), m_fov(fov), m_aspectRatio(aspect),
      m_nearPlane(nearPlane), m_farPlane(farPlane), m_focalDistance(0.0f), m_aperture(0.0f) {
    
    setTransform(position, target, up);
    updateProjection();
}

void PerspectiveCamera::setTransform(const Point3& position, const Point3& target, const Vec3& up) {
    m_position = position;
    m_target = target;
    m_up = up.normalized();
    
    updateCameraVectors();
}

void PerspectiveCamera::updateCameraVectors() {
    // Compute camera coordinate system
    m_forward = (m_target - m_position).normalized();
    m_right = m_forward.cross(m_up).normalized();
    m_up = m_right.cross(m_forward).normalized();
}

void PerspectiveCamera::updateProjection() {
    m_tanHalfFOV = std::tan(m_fov * M_PI / 360.0f); // Convert to radians and half angle
    m_imageHeight = 2.0f * m_tanHalfFOV;
    m_imageWidth = m_imageHeight * m_aspectRatio;
}

void PerspectiveCamera::setFOV(float fov) {
    m_fov = std::clamp(fov, 1.0f, 179.0f);
    updateProjection();
}

void PerspectiveCamera::setAspectRatio(float aspect) {
    m_aspectRatio = std::max(aspect, 0.1f);
    updateProjection();
}

Ray PerspectiveCamera::generateRay(float u, float v, Sampler& sampler) const {
    // Convert screen coordinates to camera space
    float x = (u - 0.5f) * m_imageWidth;
    float y = (v - 0.5f) * m_imageHeight;
    
    // Ray direction in camera space (pointing towards negative Z)
    Vec3 direction = Vec3(x, y, -1.0f).normalized();
    
    // Transform to world space
    Vec3 worldDirection = direction.x * m_right + direction.y * m_up + direction.z * m_forward;
    worldDirection.normalize();
    
    Point3 origin = m_position;
    
    // Handle depth of field if enabled
    if (hasDepthOfField()) {
        // Find focal point
        Point3 focalPoint = m_position + worldDirection * m_focalDistance;
        
        // Sample point on aperture
        Point3 aperturePoint = sampleAperture(sampler);
        
        // New origin and direction
        origin = m_position + aperturePoint;
        worldDirection = (focalPoint - origin).normalized();
    }
    
    return Ray(origin, worldDirection, m_nearPlane, m_farPlane);
}

RayDifferential PerspectiveCamera::generateRayDifferential(float u, float v, Sampler& sampler) const {
    // Generate main ray
    RayDifferential rd(generateRay(u, v, sampler));
    
    // Pixel spacing in screen coordinates
    const float pixelSpacing = 1.0f / 1024.0f; // Assume 1024x1024 for differential estimation
    
    // Generate offset rays
    Ray rxRay = generateRay(u + pixelSpacing, v, sampler);
    Ray ryRay = generateRay(u, v + pixelSpacing, sampler);
    
    rd.hasDifferentials = true;
    rd.rxOrigin = rxRay.o;
    rd.ryOrigin = ryRay.o;
    rd.rxDirection = rxRay.d;
    rd.ryDirection = ryRay.d;
    
    return rd;
}

void PerspectiveCamera::setDepthOfField(float focalDistance, float aperture) {
    m_focalDistance = std::max(focalDistance, m_nearPlane);
    m_aperture = std::max(aperture, 0.0f);
}

void PerspectiveCamera::disableDepthOfField() {
    m_aperture = 0.0f;
}

Point3 PerspectiveCamera::sampleAperture(Sampler& sampler) const {
    if (m_aperture <= 0.0f) return Point3(0);
    
    // Sample unit disk
    float r = std::sqrt(sampler.get1D()) * m_aperture;
    float theta = 2.0f * M_PI * sampler.get1D();
    
    float x = r * std::cos(theta);
    float y = r * std::sin(theta);
    
    // Transform to camera space
    return x * m_right + y * m_up;
}

// OrthographicCamera implementation
OrthographicCamera::OrthographicCamera(const Point3& position, const Point3& target, const Vec3& up,
                                     float width, float height, float nearPlane, float farPlane)
    : m_position(position), m_target(target), m_width(width), m_height(height),
      m_nearPlane(nearPlane), m_farPlane(farPlane) {
    
    setTransform(position, target, up);
}

void OrthographicCamera::setTransform(const Point3& position, const Point3& target, const Vec3& up) {
    m_position = position;
    m_target = target;
    updateCameraVectors();
}

void OrthographicCamera::updateCameraVectors() {
    m_forward = (m_target - m_position).normalized();
    m_right = m_forward.cross(Vec3(0, 1, 0)).normalized();
    m_up = m_right.cross(m_forward).normalized();
}

Ray OrthographicCamera::generateRay(float u, float v, Sampler& sampler) const {
    // Convert screen coordinates to camera space
    float x = (u - 0.5f) * m_width;
    float y = (v - 0.5f) * m_height;
    
    // Ray origin in world space
    Point3 origin = m_position + x * m_right + y * m_up;
    
    // Ray direction is always forward for orthographic projection
    Vec3 direction = m_forward;
    
    return Ray(origin, direction, m_nearPlane, m_farPlane);
}

void OrthographicCamera::setAspectRatio(float aspect) {
    if (aspect > 0.0f) {
        m_height = m_width / aspect;
    }
}

} // namespace photon
